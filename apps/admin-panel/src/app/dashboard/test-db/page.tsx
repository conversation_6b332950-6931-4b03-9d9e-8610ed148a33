'use client'

import { useEffect, useState } from 'react'
import { ProductQueries, CustomerQueries } from '@nutripro/database'
import { Card, CardContent, CardHeader, CardTitle } from '@nutripro/ui'

export default function TestDbPage() {
  const [products, setProducts] = useState<any[]>([])
  const [customers, setCustomers] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const testDatabase = async () => {
      try {
        setLoading(true)
        setError(null)

        const productQueries = new ProductQueries()
        const customerQueries = new CustomerQueries()

        // Test product queries
        const productsData = await productQueries.getAll()
        setProducts(productsData || [])

        // Test customer queries
        const customersData = await customerQueries.getAll()
        setCustomers(customersData || [])

      } catch (err) {
        console.error('Database test error:', err)
        setError(err instanceof Error ? err.message : 'Unknown error')
      } finally {
        setLoading(false)
      }
    }

    testDatabase()
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">Database Connection Test</h1>
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <span className="ml-4">Testing database connection...</span>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">Database Connection Test</h1>
          <Card className="border-red-200 bg-red-50">
            <CardHeader>
              <CardTitle className="text-red-800">Database Error</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-red-700">{error}</p>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Database Connection Test</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Products ({products.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {products.slice(0, 5).map((product) => (
                  <div key={product.id} className="border-b pb-2">
                    <h3 className="font-medium">{product.name}</h3>
                    <p className="text-sm text-gray-600">SKU: {product.sku}</p>
                    <p className="text-sm text-gray-600">Price: AWG {product.retail_price}</p>
                    <p className="text-sm text-gray-600">Stock: {product.stock_quantity}</p>
                    {product.brands && (
                      <p className="text-sm text-blue-600">Brand: {product.brands.name}</p>
                    )}
                  </div>
                ))}
                {products.length > 5 && (
                  <p className="text-sm text-gray-500">...and {products.length - 5} more</p>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Customers ({customers.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {customers.slice(0, 5).map((customer) => (
                  <div key={customer.id} className="border-b pb-2">
                    <h3 className="font-medium">
                      {customer.first_name} {customer.last_name}
                      {customer.company_name && ` (${customer.company_name})`}
                    </h3>
                    <p className="text-sm text-gray-600">Type: {customer.customer_type}</p>
                    <p className="text-sm text-gray-600">Email: {customer.email}</p>
                    <p className="text-sm text-gray-600">Loyalty Points: {customer.loyalty_points}</p>
                    {customer.coaches && (
                      <p className="text-sm text-blue-600">
                        Coach: {customer.coaches.first_name} {customer.coaches.last_name}
                      </p>
                    )}
                  </div>
                ))}
                {customers.length > 5 && (
                  <p className="text-sm text-gray-500">...and {customers.length - 5} more</p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="mt-8">
          <Card className="border-green-200 bg-green-50">
            <CardHeader>
              <CardTitle className="text-green-800">✅ Database Connection Successful!</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-green-700">
                Successfully connected to Supabase and retrieved data from the NutriPro database.
              </p>
              <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
                <div>
                  <strong>Products loaded:</strong> {products.length}
                </div>
                <div>
                  <strong>Customers loaded:</strong> {customers.length}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
