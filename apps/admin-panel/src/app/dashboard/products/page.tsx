'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { ProductQueries } from '@nutripro/database'
import { Button } from '@nutripro/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@nutripro/ui'
import { Input } from '@nutripro/ui'

interface Product {
  id: string
  sku: string
  name: string
  description: string | null
  retail_price: number
  wholesale_price: number | null
  stock_quantity: number
  min_stock_level: number
  is_active: boolean
  categories?: { id: string; name: string } | null
  brands?: { id: string; name: string } | null
  vendors?: { id: string; name: string } | null
  product_variants?: Array<{
    id: string
    variant_name: string
    variant_value: string
    stock_quantity: number
  }>
}

export default function ProductsPage() {
  const router = useRouter()
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadProducts()
  }, [])

  const loadProducts = async () => {
    try {
      setLoading(true)
      setError(null)
      const productQueries = new ProductQueries()
      const data = await productQueries.getAll()
      setProducts(data || [])
    } catch (err) {
      console.error('Failed to load products:', err)
      setError(err instanceof Error ? err.message : 'Failed to load products')
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      loadProducts()
      return
    }

    try {
      setLoading(true)
      setError(null)
      const productQueries = new ProductQueries()
      const data = await productQueries.search(searchQuery)
      setProducts(data || [])
    } catch (err) {
      console.error('Search failed:', err)
      setError(err instanceof Error ? err.message : 'Search failed')
    } finally {
      setLoading(false)
    }
  }

  const getStockStatus = (product: Product) => {
    if (product.stock_quantity <= 0) return { status: 'Out of Stock', color: 'text-red-600' }
    if (product.stock_quantity <= product.min_stock_level) return { status: 'Low Stock', color: 'text-yellow-600' }
    return { status: 'In Stock', color: 'text-green-600' }
  }

  if (loading && products.length === 0) {
    return (
      <div className="p-4 md:p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <span className="ml-4">Loading products...</span>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-4 md:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6 md:mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Products</h1>
              <p className="text-gray-600 mt-1">Manage your inventory and product catalog</p>
            </div>
            <div className="mt-4 sm:mt-0">
              <Button className="w-full sm:w-auto">
                Add Product
              </Button>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Search Products</CardTitle>
            <CardDescription>Find products by name, SKU, or description</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
              <div className="flex gap-2">
                <Button onClick={handleSearch} disabled={loading}>
                  {loading ? 'Searching...' : 'Search'}
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setSearchQuery('')
                    loadProducts()
                  }}
                >
                  Clear
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Error State */}
        {error && (
          <Card className="mb-6 border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <p className="text-red-700">{error}</p>
            </CardContent>
          </Card>
        )}

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
          {products.map((product) => {
            const stockStatus = getStockStatus(product)
            return (
              <Card key={product.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start">
                    <div className="flex-1 min-w-0">
                      <CardTitle className="text-lg truncate">{product.name}</CardTitle>
                      <CardDescription className="text-sm">
                        SKU: {product.sku}
                      </CardDescription>
                    </div>
                    <div className={`text-sm font-medium ${stockStatus.color}`}>
                      {stockStatus.status}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {/* Brand and Category */}
                    <div className="flex flex-wrap gap-2 text-sm">
                      {product.brands && (
                        <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                          {product.brands.name}
                        </span>
                      )}
                      {product.categories && (
                        <span className="bg-green-100 text-green-800 px-2 py-1 rounded">
                          {product.categories.name}
                        </span>
                      )}
                    </div>

                    {/* Pricing */}
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="text-lg font-semibold">AWG {product.retail_price}</p>
                        {product.wholesale_price && (
                          <p className="text-sm text-gray-600">
                            Wholesale: AWG {product.wholesale_price}
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Stock Info */}
                    <div className="flex justify-between items-center text-sm">
                      <span>Stock: {product.stock_quantity}</span>
                      <span className="text-gray-500">Min: {product.min_stock_level}</span>
                    </div>

                    {/* Variants */}
                    {product.product_variants && product.product_variants.length > 0 && (
                      <div>
                        <p className="text-sm font-medium text-gray-700 mb-1">
                          Variants ({product.product_variants.length})
                        </p>
                        <div className="flex flex-wrap gap-1">
                          {product.product_variants.slice(0, 3).map((variant) => (
                            <span
                              key={variant.id}
                              className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded"
                            >
                              {variant.variant_value}
                            </span>
                          ))}
                          {product.product_variants.length > 3 && (
                            <span className="text-xs text-gray-500">
                              +{product.product_variants.length - 3} more
                            </span>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex gap-2 pt-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        Edit
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1"
                        onClick={() => router.push(`/dashboard/products/${product.id}`)}
                      >
                        View
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Empty State */}
        {!loading && products.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <div className="text-gray-500">
                <p className="text-lg font-medium mb-2">No products found</p>
                <p className="text-sm">
                  {searchQuery ? 'Try adjusting your search terms' : 'Get started by adding your first product'}
                </p>
              </div>
              <Button className="mt-4">
                Add Product
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Summary */}
        {!loading && products.length > 0 && (
          <Card className="mt-6">
            <CardContent className="pt-6">
              <div className="flex flex-wrap gap-4 text-sm text-gray-600">
                <span>Total Products: {products.length}</span>
                <span>
                  Low Stock: {products.filter(p => p.stock_quantity <= p.min_stock_level).length}
                </span>
                <span>
                  Out of Stock: {products.filter(p => p.stock_quantity <= 0).length}
                </span>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
