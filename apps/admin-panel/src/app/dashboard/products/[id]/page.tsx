'use client'

import { useEffect, useState } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { ProductQueries } from '@nutripro/database'
import { Button } from '@nutripro/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@nutripro/ui'

interface ProductDetail {
  id: string
  sku: string
  barcode: string | null
  name: string
  description: string | null
  retail_price: number
  wholesale_price: number | null
  landing_cost: number | null
  purchase_price: number | null
  purchase_currency: string
  stock_quantity: number
  min_stock_level: number
  max_stock_level: number | null
  weight: number | null
  serving_size: string | null
  servings_per_container: number | null
  ingredients: string[] | null
  allergens: string[] | null
  has_variants: boolean
  variant_type: string | null
  wholesale_available: boolean
  min_order_quantity: number
  is_active: boolean
  created_at: string
  updated_at: string
  categories?: { id: string; name: string } | null
  brands?: { id: string; name: string } | null
  vendors?: { id: string; name: string } | null
  product_variants?: Array<{
    id: string
    variant_name: string
    variant_value: string
    sku_suffix: string | null
    price_adjustment: number
    stock_quantity: number
    is_active: boolean
  }>
}

export default function ProductDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [product, setProduct] = useState<ProductDetail | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (params.id) {
      loadProduct(params.id as string)
    }
  }, [params.id])

  const loadProduct = async (id: string) => {
    try {
      setLoading(true)
      setError(null)
      const productQueries = new ProductQueries()
      const data = await productQueries.getById(id)
      setProduct(data)
    } catch (err) {
      console.error('Failed to load product:', err)
      setError(err instanceof Error ? err.message : 'Failed to load product')
    } finally {
      setLoading(false)
    }
  }

  const getStockStatus = (quantity: number, minLevel: number) => {
    if (quantity <= 0) return { status: 'Out of Stock', color: 'text-red-600', bgColor: 'bg-red-100' }
    if (quantity <= minLevel) return { status: 'Low Stock', color: 'text-yellow-600', bgColor: 'bg-yellow-100' }
    return { status: 'In Stock', color: 'text-green-600', bgColor: 'bg-green-100' }
  }

  const formatCurrency = (amount: number, currency: string = 'AWG') => {
    return `${currency} ${amount.toFixed(2)}`
  }

  if (loading) {
    return (
      <div className="p-4 md:p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <span className="ml-4">Loading product...</span>
          </div>
        </div>
      </div>
    )
  }

  if (error || !product) {
    return (
      <div className="p-4 md:p-6">
        <div className="max-w-7xl mx-auto">
          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <p className="text-red-700">{error || 'Product not found'}</p>
              <Button
                variant="outline"
                onClick={() => router.push('/dashboard/products')}
                className="mt-4"
              >
                Back to Products
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  const stockStatus = getStockStatus(product.stock_quantity, product.min_stock_level)

  return (
    <div className="p-4 md:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6 md:mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <Button
                variant="outline"
                onClick={() => router.push('/dashboard/products')}
                className="mb-4"
              >
                ← Back to Products
              </Button>
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900">{product.name}</h1>
              <p className="text-gray-600 mt-1">SKU: {product.sku}</p>
            </div>
            <div className="mt-4 sm:mt-0 flex gap-2">
              <Button variant="outline">
                Edit Product
              </Button>
              <Button>
                Update Stock
              </Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Product Info */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Product Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Product Name</label>
                    <p className="text-gray-900">{product.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">SKU</label>
                    <p className="text-gray-900">{product.sku}</p>
                  </div>
                  {product.barcode && (
                    <div>
                      <label className="text-sm font-medium text-gray-700">Barcode</label>
                      <p className="text-gray-900">{product.barcode}</p>
                    </div>
                  )}
                  <div>
                    <label className="text-sm font-medium text-gray-700">Brand</label>
                    <p className="text-gray-900">{product.brands?.name || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Category</label>
                    <p className="text-gray-900">{product.categories?.name || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Vendor</label>
                    <p className="text-gray-900">{product.vendors?.name || 'N/A'}</p>
                  </div>
                </div>
                
                {product.description && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Description</label>
                    <p className="text-gray-900 mt-1">{product.description}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Pricing Information */}
            <Card>
              <CardHeader>
                <CardTitle>Pricing & Costs</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Retail Price</label>
                    <p className="text-lg font-semibold text-gray-900">
                      {formatCurrency(product.retail_price)}
                    </p>
                  </div>
                  {product.wholesale_price && (
                    <div>
                      <label className="text-sm font-medium text-gray-700">Wholesale Price</label>
                      <p className="text-lg font-semibold text-gray-900">
                        {formatCurrency(product.wholesale_price)}
                      </p>
                    </div>
                  )}
                  {product.landing_cost && (
                    <div>
                      <label className="text-sm font-medium text-gray-700">Landing Cost</label>
                      <p className="text-gray-900">
                        {formatCurrency(product.landing_cost)}
                      </p>
                    </div>
                  )}
                  {product.purchase_price && (
                    <div>
                      <label className="text-sm font-medium text-gray-700">Purchase Price</label>
                      <p className="text-gray-900">
                        {formatCurrency(product.purchase_price, product.purchase_currency)}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Product Variants */}
            {product.has_variants && product.product_variants && product.product_variants.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Product Variants</CardTitle>
                  <CardDescription>
                    {product.variant_type} variants for this product
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {product.product_variants.map((variant) => {
                      const variantStockStatus = getStockStatus(variant.stock_quantity, product.min_stock_level)
                      return (
                        <div key={variant.id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex-1">
                            <p className="font-medium">{variant.variant_value}</p>
                            <p className="text-sm text-gray-600">
                              SKU: {product.sku}{variant.sku_suffix || ''}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="font-medium">Stock: {variant.stock_quantity}</p>
                            <span className={`text-xs px-2 py-1 rounded ${variantStockStatus.bgColor} ${variantStockStatus.color}`}>
                              {variantStockStatus.status}
                            </span>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Stock Status */}
            <Card>
              <CardHeader>
                <CardTitle>Stock Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${stockStatus.bgColor} ${stockStatus.color}`}>
                      {stockStatus.status}
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Current Stock</span>
                      <span className="font-medium">{product.stock_quantity}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Minimum Level</span>
                      <span className="font-medium">{product.min_stock_level}</span>
                    </div>
                    {product.max_stock_level && (
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Maximum Level</span>
                        <span className="font-medium">{product.max_stock_level}</span>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Product Details */}
            <Card>
              <CardHeader>
                <CardTitle>Product Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {product.weight && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Weight</span>
                    <span className="text-sm">{product.weight}g</span>
                  </div>
                )}
                {product.serving_size && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Serving Size</span>
                    <span className="text-sm">{product.serving_size}</span>
                  </div>
                )}
                {product.servings_per_container && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Servings</span>
                    <span className="text-sm">{product.servings_per_container}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Wholesale Available</span>
                  <span className="text-sm">{product.wholesale_available ? 'Yes' : 'No'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Min Order Qty</span>
                  <span className="text-sm">{product.min_order_quantity}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Status</span>
                  <span className={`text-sm ${product.is_active ? 'text-green-600' : 'text-red-600'}`}>
                    {product.is_active ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* Ingredients & Allergens */}
            {(product.ingredients || product.allergens) && (
              <Card>
                <CardHeader>
                  <CardTitle>Ingredients & Allergens</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {product.ingredients && (
                    <div>
                      <label className="text-sm font-medium text-gray-700">Ingredients</label>
                      <div className="mt-1 flex flex-wrap gap-1">
                        {product.ingredients.map((ingredient, index) => (
                          <span key={index} className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                            {ingredient}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                  {product.allergens && (
                    <div>
                      <label className="text-sm font-medium text-gray-700">Allergens</label>
                      <div className="mt-1 flex flex-wrap gap-1">
                        {product.allergens.map((allergen, index) => (
                          <span key={index} className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
                            {allergen}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
