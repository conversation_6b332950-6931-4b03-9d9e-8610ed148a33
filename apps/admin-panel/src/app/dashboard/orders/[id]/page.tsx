'use client'

import { useEffect, useState } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { TransactionQueries, type TransactionWithItems } from '@nutripro/database'
import { Button } from '@nutripro/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@nutripro/ui'
import { Badge } from '@nutripro/ui'
import { ArrowLeft, Edit, Printer, RefreshCw, X } from 'lucide-react'

export default function OrderDetailPage() {
  const router = useRouter()
  const params = useParams()
  const orderId = params.id as string

  const [order, setOrder] = useState<TransactionWithItems | null>(null)
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)

  useEffect(() => {
    if (orderId) {
      loadOrder()
    }
  }, [orderId])

  const loadOrder = async () => {
    try {
      setLoading(true)
      const transactionQueries = new TransactionQueries()
      const data = await transactionQueries.getById(orderId)
      setOrder(data)
    } catch (error) {
      console.error('Failed to load order:', error)
    } finally {
      setLoading(false)
    }
  }

  const updateOrderStatus = async (newStatus: string) => {
    if (!order) return

    try {
      setUpdating(true)
      const transactionQueries = new TransactionQueries()
      await transactionQueries.updateStatus(order.id, newStatus)
      await loadOrder() // Reload to get updated data
    } catch (error) {
      console.error('Failed to update order status:', error)
    } finally {
      setUpdating(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 border-green-200'
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'processing': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'cancelled': return 'bg-red-100 text-red-800 border-red-200'
      case 'refunded': return 'bg-gray-100 text-gray-800 border-gray-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const formatCurrency = (amount: number) => {
    return `AWG ${amount.toFixed(2)}`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getCustomerInfo = () => {
    if (!order?.customers) return { name: 'Walk-in Customer', type: 'retail' }
    
    const customer = order.customers
    const name = customer.company_name || 
                 `${customer.first_name || ''} ${customer.last_name || ''}`.trim() || 
                 'Unknown Customer'
    
    return { name, type: customer.customer_type }
  }

  if (loading) {
    return (
      <div className="p-4 md:p-6">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="p-4 md:p-6">
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardContent className="p-8 text-center">
              <div className="text-gray-500">
                <div className="text-4xl mb-4">❌</div>
                <h3 className="text-lg font-medium mb-2">Order not found</h3>
                <p className="text-sm mb-4">The order you're looking for doesn't exist or has been deleted.</p>
                <Button onClick={() => router.push('/orders')}>
                  Back to Orders
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  const customerInfo = getCustomerInfo()

  return (
    <div className="p-4 md:p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center space-x-4 mb-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push('/orders')}
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to Orders
            </Button>
          </div>
          
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900">
                Order {order.transaction_number}
              </h1>
              <p className="text-gray-600 mt-1">
                Created on {formatDate(order.created_at)}
              </p>
            </div>
            
            <div className="flex items-center space-x-2">
              <Badge className={getStatusColor(order.status)}>
                {order.status}
              </Badge>
              <Button variant="outline" size="sm">
                <Printer className="h-4 w-4 mr-1" />
                Print
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push(`/orders/${order.id}/edit`)}
              >
                <Edit className="h-4 w-4 mr-1" />
                Edit
              </Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Order Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Order Items */}
            <Card>
              <CardHeader>
                <CardTitle>Order Items</CardTitle>
                <CardDescription>
                  {order.transaction_items?.length || 0} items in this order
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {order.transaction_items?.map((item) => (
                    <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <h4 className="font-medium">{item.product_name}</h4>
                        <p className="text-sm text-gray-600">SKU: {item.product_sku}</p>
                        {item.batch_number && (
                          <p className="text-xs text-gray-500">Batch: {item.batch_number}</p>
                        )}
                      </div>
                      <div className="text-right">
                        <p className="font-medium">
                          {item.quantity} × {formatCurrency(item.unit_price)}
                        </p>
                        {item.discount_amount > 0 && (
                          <p className="text-sm text-red-600">
                            -{formatCurrency(item.discount_amount)} discount
                          </p>
                        )}
                        <p className="font-semibold">
                          {formatCurrency(item.line_total)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Order Notes */}
            {(order.notes || order.internal_notes) && (
              <Card>
                <CardHeader>
                  <CardTitle>Notes</CardTitle>
                </CardHeader>
                <CardContent>
                  {order.notes && (
                    <div className="mb-4">
                      <h4 className="font-medium text-sm text-gray-700 mb-1">Customer Notes:</h4>
                      <p className="text-sm">{order.notes}</p>
                    </div>
                  )}
                  {order.internal_notes && (
                    <div>
                      <h4 className="font-medium text-sm text-gray-700 mb-1">Internal Notes:</h4>
                      <p className="text-sm">{order.internal_notes}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Order Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Order Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>{formatCurrency(order.subtotal)}</span>
                </div>
                {order.discount_amount > 0 && (
                  <div className="flex justify-between text-red-600">
                    <span>Discount:</span>
                    <span>-{formatCurrency(order.discount_amount)}</span>
                  </div>
                )}
                {order.tax_amount > 0 && (
                  <div className="flex justify-between">
                    <span>Tax ({(order.tax_rate * 100).toFixed(1)}%):</span>
                    <span>{formatCurrency(order.tax_amount)}</span>
                  </div>
                )}
                <div className="border-t pt-3">
                  <div className="flex justify-between font-semibold text-lg">
                    <span>Total:</span>
                    <span>{formatCurrency(order.total_amount)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Customer Information */}
            <Card>
              <CardHeader>
                <CardTitle>Customer Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div>
                    <span className="font-medium">Name:</span>
                    <p>{customerInfo.name}</p>
                  </div>
                  <div>
                    <span className="font-medium">Type:</span>
                    <p className="capitalize">{customerInfo.type}</p>
                  </div>
                  {order.customers?.email && (
                    <div>
                      <span className="font-medium">Email:</span>
                      <p>{order.customers.email}</p>
                    </div>
                  )}
                  {order.customers?.phone && (
                    <div>
                      <span className="font-medium">Phone:</span>
                      <p>{order.customers.phone}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Payment Information */}
            <Card>
              <CardHeader>
                <CardTitle>Payment Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div>
                    <span className="font-medium">Method:</span>
                    <p className="capitalize">{order.payment_method || 'Not specified'}</p>
                  </div>
                  {order.payment_reference && (
                    <div>
                      <span className="font-medium">Reference:</span>
                      <p>{order.payment_reference}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Status Actions */}
            {order.status !== 'completed' && order.status !== 'cancelled' && (
              <Card>
                <CardHeader>
                  <CardTitle>Order Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {order.status === 'pending' && (
                    <Button
                      onClick={() => updateOrderStatus('processing')}
                      disabled={updating}
                      className="w-full"
                    >
                      <RefreshCw className="h-4 w-4 mr-1" />
                      Mark as Processing
                    </Button>
                  )}
                  {(order.status === 'pending' || order.status === 'processing') && (
                    <Button
                      onClick={() => updateOrderStatus('completed')}
                      disabled={updating}
                      className="w-full"
                    >
                      ✓ Mark as Completed
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    onClick={() => updateOrderStatus('cancelled')}
                    disabled={updating}
                    className="w-full"
                  >
                    <X className="h-4 w-4 mr-1" />
                    Cancel Order
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
