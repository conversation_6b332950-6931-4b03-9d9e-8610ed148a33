'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { CustomerQueries } from '@nutripro/database'
import { Button } from '@nutripro/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@nutripro/ui'
import { Input } from '@nutripro/ui'

interface Customer {
  id: string
  customer_type: 'retail' | 'wholesale'
  first_name: string | null
  last_name: string | null
  company_name: string | null
  email: string | null
  phone: string | null
  address_line1: string | null
  city: string | null
  postal_code: string | null
  country: string
  loyalty_points: number
  store_credit: number
  membership_tier: string
  membership_expires_at: string | null
  assigned_coach_id: string | null
  is_active: boolean
  created_at: string
  coaches?: {
    id: string
    first_name: string
    last_name: string
  } | null
}

export default function CustomersPage() {
  const router = useRouter()
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [filterType, setFilterType] = useState<'all' | 'retail' | 'wholesale'>('all')
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadCustomers()
  }, [filterType])

  const loadCustomers = async () => {
    try {
      setLoading(true)
      setError(null)
      const customerQueries = new CustomerQueries()
      
      let data
      if (filterType === 'all') {
        data = await customerQueries.getAll()
      } else {
        data = await customerQueries.getByType(filterType)
      }
      
      setCustomers(data || [])
    } catch (err) {
      console.error('Failed to load customers:', err)
      setError(err instanceof Error ? err.message : 'Failed to load customers')
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      loadCustomers()
      return
    }

    try {
      setLoading(true)
      setError(null)
      const customerQueries = new CustomerQueries()
      const data = await customerQueries.search(searchQuery)
      setCustomers(data || [])
    } catch (err) {
      console.error('Search failed:', err)
      setError(err instanceof Error ? err.message : 'Search failed')
    } finally {
      setLoading(false)
    }
  }

  const getCustomerName = (customer: Customer) => {
    if (customer.customer_type === 'wholesale' && customer.company_name) {
      return customer.company_name
    }
    return `${customer.first_name || ''} ${customer.last_name || ''}`.trim() || 'Unnamed Customer'
  }

  const getMembershipBadgeColor = (tier: string) => {
    switch (tier) {
      case 'premium': return 'bg-purple-100 text-purple-800'
      case 'regular': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getCustomerTypeBadgeColor = (type: string) => {
    switch (type) {
      case 'wholesale': return 'bg-green-100 text-green-800'
      case 'retail': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading && customers.length === 0) {
    return (
      <div className="p-4 md:p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <span className="ml-4">Loading customers...</span>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-4 md:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6 md:mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Customers</h1>
              <p className="text-gray-600 mt-1">Manage your customer database and relationships</p>
            </div>
            <div className="mt-4 sm:mt-0">
              <Button className="w-full sm:w-auto">
                Add Customer
              </Button>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Search & Filter Customers</CardTitle>
            <CardDescription>Find customers by name, email, company, or filter by type</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Search */}
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search customers..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  />
                </div>
                <div className="flex gap-2">
                  <Button onClick={handleSearch} disabled={loading}>
                    {loading ? 'Searching...' : 'Search'}
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => {
                      setSearchQuery('')
                      loadCustomers()
                    }}
                  >
                    Clear
                  </Button>
                </div>
              </div>

              {/* Filters */}
              <div className="flex flex-wrap gap-2">
                <Button
                  variant={filterType === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilterType('all')}
                >
                  All Customers
                </Button>
                <Button
                  variant={filterType === 'retail' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilterType('retail')}
                >
                  Retail
                </Button>
                <Button
                  variant={filterType === 'wholesale' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilterType('wholesale')}
                >
                  Wholesale
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Error State */}
        {error && (
          <Card className="mb-6 border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <p className="text-red-700">{error}</p>
            </CardContent>
          </Card>
        )}

        {/* Customers Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
          {customers.map((customer) => (
            <Card key={customer.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-lg truncate">
                      {getCustomerName(customer)}
                    </CardTitle>
                    <CardDescription className="text-sm">
                      {customer.email || 'No email provided'}
                    </CardDescription>
                  </div>
                  <div className="flex flex-col gap-1">
                    <span className={`text-xs px-2 py-1 rounded-full ${getCustomerTypeBadgeColor(customer.customer_type)}`}>
                      {customer.customer_type}
                    </span>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {/* Contact Info */}
                  <div className="space-y-1">
                    {customer.phone && (
                      <p className="text-sm text-gray-600">📞 {customer.phone}</p>
                    )}
                    {customer.address_line1 && (
                      <p className="text-sm text-gray-600">
                        📍 {customer.address_line1}
                        {customer.city && `, ${customer.city}`}
                      </p>
                    )}
                  </div>

                  {/* Membership & Loyalty */}
                  <div className="flex justify-between items-center">
                    <div>
                      <span className={`text-xs px-2 py-1 rounded ${getMembershipBadgeColor(customer.membership_tier)}`}>
                        {customer.membership_tier} member
                      </span>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{customer.loyalty_points} points</p>
                      {customer.store_credit > 0 && (
                        <p className="text-xs text-green-600">AWG {customer.store_credit.toFixed(2)} credit</p>
                      )}
                    </div>
                  </div>

                  {/* Coach Assignment */}
                  {customer.coaches && (
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-gray-500">Coach:</span>
                      <span className="text-xs bg-blue-50 text-blue-700 px-2 py-1 rounded">
                        {customer.coaches.first_name} {customer.coaches.last_name}
                      </span>
                    </div>
                  )}

                  {/* Membership Expiry */}
                  {customer.membership_expires_at && (
                    <div className="text-xs text-gray-500">
                      Membership expires: {new Date(customer.membership_expires_at).toLocaleDateString()}
                    </div>
                  )}

                  {/* Actions */}
                  <div className="flex gap-2 pt-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      Edit
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="flex-1"
                      onClick={() => router.push(`/customers/${customer.id}`)}
                    >
                      View
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {!loading && customers.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <div className="text-gray-500">
                <p className="text-lg font-medium mb-2">No customers found</p>
                <p className="text-sm">
                  {searchQuery ? 'Try adjusting your search terms' : 'Get started by adding your first customer'}
                </p>
              </div>
              <Button className="mt-4">
                Add Customer
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Summary */}
        {!loading && customers.length > 0 && (
          <Card className="mt-6">
            <CardContent className="pt-6">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="text-center">
                  <p className="font-medium text-gray-900">{customers.length}</p>
                  <p className="text-gray-600">Total Customers</p>
                </div>
                <div className="text-center">
                  <p className="font-medium text-gray-900">
                    {customers.filter(c => c.customer_type === 'retail').length}
                  </p>
                  <p className="text-gray-600">Retail</p>
                </div>
                <div className="text-center">
                  <p className="font-medium text-gray-900">
                    {customers.filter(c => c.customer_type === 'wholesale').length}
                  </p>
                  <p className="text-gray-600">Wholesale</p>
                </div>
                <div className="text-center">
                  <p className="font-medium text-gray-900">
                    {customers.filter(c => c.membership_tier === 'premium').length}
                  </p>
                  <p className="text-gray-600">Premium Members</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
