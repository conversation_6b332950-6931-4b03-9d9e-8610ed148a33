'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { PurchaseOrderQueries, ProductQueries, type CreatePurchaseOrderData } from '@nutripro/database'
import { Button } from '@nutripro/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@nutripro/ui'
import { Input } from '@nutripro/ui'
import { ArrowLeft, Plus, Trash2, Search } from 'lucide-react'

interface POItem {
  id: string
  product_id: string
  product_name: string
  product_sku: string
  quantity_ordered: number
  unit_cost: number
  line_total: number
}

interface Vendor {
  id: string
  name: string
  contact_email: string | null
  preferred_currency: string
}

interface Product {
  id: string
  name: string
  sku: string
  purchase_price: number | null
  purchase_currency: string
}

export default function NewPurchaseOrderPage() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [vendors, setVendors] = useState<Vendor[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [selectedVendor, setSelectedVendor] = useState<Vendor | null>(null)
  const [poItems, setPOItems] = useState<POItem[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [expectedDeliveryDate, setExpectedDeliveryDate] = useState('')
  const [notes, setNotes] = useState('')
  const [shippingCost, setShippingCost] = useState(0)

  useEffect(() => {
    loadInitialData()
  }, [])

  const loadInitialData = async () => {
    try {
      const productQueries = new ProductQueries()
      
      // For now, we'll use mock vendor data since we don't have vendor queries yet
      const mockVendors: Vendor[] = [
        { id: '1', name: 'Optimum Nutrition', contact_email: '<EMAIL>', preferred_currency: 'USD' },
        { id: '2', name: 'MuscleTech', contact_email: '<EMAIL>', preferred_currency: 'USD' },
        { id: '3', name: 'BSN', contact_email: '<EMAIL>', preferred_currency: 'USD' },
        { id: '4', name: 'NOW Foods', contact_email: '<EMAIL>', preferred_currency: 'USD' }
      ]
      
      const productsData = await productQueries.getAll()
      
      setVendors(mockVendors)
      setProducts(productsData || [])
    } catch (error) {
      console.error('Failed to load initial data:', error)
    }
  }

  const addProductToPO = (product: Product) => {
    const existingItem = poItems.find(item => item.product_id === product.id)
    
    if (existingItem) {
      // Increase quantity if product already exists
      updateItemQuantity(existingItem.id, existingItem.quantity_ordered + 1)
    } else {
      // Add new item
      const unitCost = product.purchase_price || 0
      
      const newItem: POItem = {
        id: `temp-${Date.now()}`,
        product_id: product.id,
        product_name: product.name,
        product_sku: product.sku,
        quantity_ordered: 1,
        unit_cost: unitCost,
        line_total: unitCost
      }
      
      setPOItems([...poItems, newItem])
    }
  }

  const updateItemQuantity = (itemId: string, quantity: number) => {
    if (quantity <= 0) {
      removeItem(itemId)
      return
    }
    
    setPOItems(items => 
      items.map(item => 
        item.id === itemId 
          ? { ...item, quantity_ordered: quantity, line_total: item.unit_cost * quantity }
          : item
      )
    )
  }

  const updateItemCost = (itemId: string, cost: number) => {
    setPOItems(items => 
      items.map(item => 
        item.id === itemId 
          ? { ...item, unit_cost: cost, line_total: cost * item.quantity_ordered }
          : item
      )
    )
  }

  const removeItem = (itemId: string) => {
    setPOItems(items => items.filter(item => item.id !== itemId))
  }

  const calculateTotals = () => {
    const subtotal = poItems.reduce((sum, item) => sum + item.line_total, 0)
    const total = subtotal + shippingCost
    
    return { subtotal, total }
  }

  const createPurchaseOrder = async () => {
    if (!selectedVendor || poItems.length === 0) {
      alert('Please select a vendor and add at least one item')
      return
    }

    try {
      setLoading(true)
      const { subtotal, total } = calculateTotals()
      
      const poData: CreatePurchaseOrderData = {
        purchase_order: {
          po_number: '', // Will be auto-generated
          vendor_id: selectedVendor.id,
          status: 'draft',
          expected_delivery_date: expectedDeliveryDate || null,
          currency: selectedVendor.preferred_currency,
          subtotal,
          shipping_cost: shippingCost,
          total_amount: total,
          notes: notes || null
        },
        items: poItems.map(item => ({
          product_id: item.product_id,
          quantity_ordered: item.quantity_ordered,
          unit_cost: item.unit_cost,
          line_total: item.line_total,
          product_name: item.product_name,
          product_sku: item.product_sku
        }))
      }

      const poQueries = new PurchaseOrderQueries()
      const newPO = await poQueries.create(poData)
      
      // Redirect to the new PO detail page
      router.push(`/purchase-orders/${newPO.id}`)
    } catch (error) {
      console.error('Failed to create purchase order:', error)
      alert('Failed to create purchase order. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    product.sku.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const { subtotal, total } = calculateTotals()

  return (
    <div className="p-4 md:p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center space-x-4 mb-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push('/purchase-orders')}
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to Purchase Orders
            </Button>
          </div>
          
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Create Purchase Order</h1>
          <p className="text-gray-600 mt-1">Order products from vendors to restock inventory</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Product Selection */}
          <div className="lg:col-span-2 space-y-6">
            {/* Vendor Selection */}
            <Card>
              <CardHeader>
                <CardTitle>Vendor Information</CardTitle>
                <CardDescription>Select the vendor for this purchase order</CardDescription>
              </CardHeader>
              <CardContent>
                <select
                  value={selectedVendor?.id || ''}
                  onChange={(e) => {
                    const vendor = vendors.find(v => v.id === e.target.value)
                    setSelectedVendor(vendor || null)
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select a vendor</option>
                  {vendors.map(vendor => (
                    <option key={vendor.id} value={vendor.id}>
                      {vendor.name} ({vendor.preferred_currency})
                    </option>
                  ))}
                </select>
              </CardContent>
            </Card>

            {/* Product Search */}
            <Card>
              <CardHeader>
                <CardTitle>Add Products</CardTitle>
                <CardDescription>Search and add products to the purchase order</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="mb-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search products by name or SKU..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                
                <div className="max-h-64 overflow-y-auto space-y-2">
                  {filteredProducts.map(product => (
                    <div key={product.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                      <div className="flex-1">
                        <h4 className="font-medium">{product.name}</h4>
                        <p className="text-sm text-gray-600">SKU: {product.sku}</p>
                      </div>
                      <div className="text-right mr-4">
                        <p className="font-medium">
                          {product.purchase_currency} {(product.purchase_price || 0).toFixed(2)}
                        </p>
                        <p className="text-xs text-gray-500">Purchase Price</p>
                      </div>
                      <Button
                        size="sm"
                        onClick={() => addProductToPO(product)}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* PO Items */}
            <Card>
              <CardHeader>
                <CardTitle>Purchase Order Items</CardTitle>
                <CardDescription>{poItems.length} items in order</CardDescription>
              </CardHeader>
              <CardContent>
                {poItems.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <div className="text-4xl mb-4">📦</div>
                    <p>No items added yet</p>
                    <p className="text-sm">Search and add products above</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {poItems.map(item => (
                      <div key={item.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                        <div className="flex-1">
                          <h4 className="font-medium">{item.product_name}</h4>
                          <p className="text-sm text-gray-600">SKU: {item.product_sku}</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Input
                            type="number"
                            min="1"
                            value={item.quantity_ordered}
                            onChange={(e) => updateItemQuantity(item.id, parseInt(e.target.value) || 1)}
                            className="w-20"
                          />
                          <span className="text-sm">×</span>
                          <Input
                            type="number"
                            min="0"
                            step="0.01"
                            value={item.unit_cost}
                            onChange={(e) => updateItemCost(item.id, parseFloat(e.target.value) || 0)}
                            className="w-24"
                          />
                        </div>
                        <div className="text-right">
                          <p className="font-medium">
                            {selectedVendor?.preferred_currency} {item.line_total.toFixed(2)}
                          </p>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => removeItem(item.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Order Summary */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Order Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>{selectedVendor?.preferred_currency} {subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Shipping:</span>
                  <Input
                    type="number"
                    min="0"
                    step="0.01"
                    value={shippingCost}
                    onChange={(e) => setShippingCost(parseFloat(e.target.value) || 0)}
                    className="w-24 text-right"
                  />
                </div>
                <div className="border-t pt-3">
                  <div className="flex justify-between font-semibold text-lg">
                    <span>Total:</span>
                    <span>{selectedVendor?.preferred_currency} {total.toFixed(2)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Order Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Expected Delivery Date</label>
                  <Input
                    type="date"
                    value={expectedDeliveryDate}
                    onChange={(e) => setExpectedDeliveryDate(e.target.value)}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">Notes</label>
                  <textarea
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    placeholder="Order notes..."
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </CardContent>
            </Card>

            <Button
              onClick={createPurchaseOrder}
              disabled={loading || !selectedVendor || poItems.length === 0}
              className="w-full"
              size="lg"
            >
              {loading ? 'Creating Purchase Order...' : 'Create Purchase Order'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
