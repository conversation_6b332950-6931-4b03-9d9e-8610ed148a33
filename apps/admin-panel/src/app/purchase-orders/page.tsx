'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { PurchaseOrderQueries, type PurchaseOrderWithItems } from '@nutripro/database'
import { Button } from '@nutripro/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@nutripro/ui'
import { Badge } from '@nutripro/ui'
import { Plus, Search, Filter, Eye, Edit, Package } from 'lucide-react'
import { Input } from '@nutripro/ui'

export default function PurchaseOrdersPage() {
  const router = useRouter()
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrderWithItems[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')

  useEffect(() => {
    loadPurchaseOrders()
  }, [])

  const loadPurchaseOrders = async () => {
    try {
      setLoading(true)
      const poQueries = new PurchaseOrderQueries()
      const data = await poQueries.getAll(50, 0)
      setPurchaseOrders(data)
    } catch (error) {
      console.error('Failed to load purchase orders:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      loadPurchaseOrders()
      return
    }

    try {
      setLoading(true)
      const poQueries = new PurchaseOrderQueries()
      const data = await poQueries.search(query)
      setPurchaseOrders(data)
    } catch (error) {
      console.error('Search failed:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'received': return 'bg-green-100 text-green-800 border-green-200'
      case 'partially_received': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'sent': return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'approved': return 'bg-indigo-100 text-indigo-800 border-indigo-200'
      case 'pending_approval': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'draft': return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'cancelled': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const formatCurrency = (amount: number, currency: string = 'AWG') => {
    return `${currency} ${amount.toFixed(2)}`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getVendorName = (po: PurchaseOrderWithItems) => {
    return po.vendors?.name || 'Unknown Vendor'
  }

  const filteredPOs = purchaseOrders.filter(po => {
    if (statusFilter !== 'all' && po.status !== statusFilter) {
      return false
    }
    return true
  })

  if (loading) {
    return (
      <div className="p-4 md:p-6">
        <div className="max-w-7xl mx-auto">
          <div className="mb-6">
            <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Purchase Orders</h1>
            <p className="text-gray-600 mt-1">Manage vendor orders and inventory purchasing</p>
          </div>
          
          <div className="space-y-4">
            {[1, 2, 3, 4, 5].map((i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/3"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-4 md:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6 md:mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Purchase Orders</h1>
              <p className="text-gray-600 mt-1">Manage vendor orders and inventory purchasing</p>
            </div>
            <Button 
              onClick={() => router.push('/purchase-orders/new')}
              className="flex items-center space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span>New Purchase Order</span>
            </Button>
          </div>
        </div>

        {/* Filters and Search */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search purchase orders by PO number or notes..."
                    value={searchQuery}
                    onChange={(e) => {
                      setSearchQuery(e.target.value)
                      if (e.target.value === '') {
                        loadPurchaseOrders()
                      }
                    }}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        handleSearch(searchQuery)
                      }
                    }}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Status</option>
                  <option value="draft">Draft</option>
                  <option value="pending_approval">Pending Approval</option>
                  <option value="approved">Approved</option>
                  <option value="sent">Sent</option>
                  <option value="partially_received">Partially Received</option>
                  <option value="received">Received</option>
                  <option value="cancelled">Cancelled</option>
                </select>
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Purchase Orders List */}
        <div className="space-y-4">
          {filteredPOs.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <div className="text-gray-500">
                  <div className="text-4xl mb-4">📋</div>
                  <h3 className="text-lg font-medium mb-2">No purchase orders found</h3>
                  <p className="text-sm">
                    {searchQuery ? 'Try adjusting your search criteria' : 'Create your first purchase order to get started'}
                  </p>
                  {!searchQuery && (
                    <Button 
                      onClick={() => router.push('/purchase-orders/new')}
                      className="mt-4"
                    >
                      Create First Purchase Order
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ) : (
            filteredPOs.map((po) => (
              <Card key={po.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="font-semibold text-lg">
                          {po.po_number}
                        </h3>
                        <Badge className={getStatusColor(po.status)}>
                          {po.status.replace('_', ' ')}
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-600">
                        <div>
                          <span className="font-medium">Vendor:</span>
                          <br />
                          {getVendorName(po)}
                        </div>
                        <div>
                          <span className="font-medium">Items:</span>
                          <br />
                          {po.purchase_order_items?.length || 0} items
                        </div>
                        <div>
                          <span className="font-medium">Total:</span>
                          <br />
                          <span className="font-semibold text-gray-900">
                            {formatCurrency(po.total_amount, po.currency)}
                          </span>
                          {po.total_amount_awg && po.currency !== 'AWG' && (
                            <div className="text-xs text-gray-500">
                              {formatCurrency(po.total_amount_awg, 'AWG')}
                            </div>
                          )}
                        </div>
                        <div>
                          <span className="font-medium">Order Date:</span>
                          <br />
                          {formatDate(po.order_date)}
                          {po.expected_delivery_date && (
                            <div className="text-xs text-gray-500">
                              Expected: {formatDate(po.expected_delivery_date)}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.push(`/purchase-orders/${po.id}`)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                      {(po.status === 'draft' || po.status === 'pending_approval') && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/purchase-orders/${po.id}/edit`)}
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                      )}
                      {(po.status === 'sent' || po.status === 'partially_received') && (
                        <Button
                          size="sm"
                          onClick={() => router.push(`/purchase-orders/${po.id}/receive`)}
                        >
                          <Package className="h-4 w-4 mr-1" />
                          Receive
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* Load More */}
        {filteredPOs.length > 0 && filteredPOs.length >= 50 && (
          <div className="mt-6 text-center">
            <Button variant="outline" onClick={() => loadPurchaseOrders()}>
              Load More Purchase Orders
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
