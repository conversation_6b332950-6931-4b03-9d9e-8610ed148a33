"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth-context */ \"(app-pages-browser)/./src/lib/auth-context.tsx\");\n/* harmony import */ var _nutripro_database__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @nutripro/database */ \"(app-pages-browser)/../../packages/database/src/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button!=!@nutripro/ui */ \"(app-pages-browser)/__barrel_optimize__?names=Button!=!../../packages/ui/src/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardContent,CardDescription,CardHeader,CardTitle!=!@nutripro/ui */ \"(app-pages-browser)/__barrel_optimize__?names=Card,CardContent,CardDescription,CardHeader,CardTitle!=!../../packages/ui/src/index.ts\");\n/* harmony import */ var _components_dashboard_SalesMetricsSection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/SalesMetricsSection */ \"(app-pages-browser)/./src/components/dashboard/SalesMetricsSection.tsx\");\n/* harmony import */ var _components_dashboard_RevenueChartsSection__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/RevenueChartsSection */ \"(app-pages-browser)/./src/components/dashboard/RevenueChartsSection.tsx\");\n/* harmony import */ var _components_dashboard_InventoryAlertsSection__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/dashboard/InventoryAlertsSection */ \"(app-pages-browser)/./src/components/dashboard/InventoryAlertsSection.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const { user } = (0,_lib_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalProducts: 0,\n        lowStockProducts: 0,\n        totalCustomers: 0,\n        retailCustomers: 0,\n        wholesaleCustomers: 0,\n        loading: true\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadStats = async ()=>{\n            try {\n                const productQueries = new _nutripro_database__WEBPACK_IMPORTED_MODULE_3__.ProductQueries();\n                const customerQueries = new _nutripro_database__WEBPACK_IMPORTED_MODULE_3__.CustomerQueries();\n                const [products, lowStock, customerStats] = await Promise.all([\n                    productQueries.getAll(),\n                    productQueries.getLowStock(10),\n                    customerQueries.getStats()\n                ]);\n                setStats({\n                    totalProducts: (products === null || products === void 0 ? void 0 : products.length) || 0,\n                    lowStockProducts: (lowStock === null || lowStock === void 0 ? void 0 : lowStock.length) || 0,\n                    totalCustomers: customerStats.total,\n                    retailCustomers: customerStats.retail,\n                    wholesaleCustomers: customerStats.wholesale,\n                    loading: false\n                });\n            } catch (error) {\n                console.error(\"Failed to load dashboard stats:\", error);\n                setStats((prev)=>({\n                        ...prev,\n                        loading: false\n                    }));\n            }\n        };\n        loadStats();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4 md:p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 md:mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl md:text-3xl font-bold text-gray-900\",\n                            children: \"Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-1\",\n                            children: [\n                                \"Welcome back, \",\n                                (user === null || user === void 0 ? void 0 : user.email) || \"Demo User\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6 md:mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: \"Total Products\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: stats.loading ? \"...\" : stats.totalProducts\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-blue-600\",\n                                            children: \"Active inventory items\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: \"Total Customers\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: stats.loading ? \"...\" : stats.totalCustomers\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-green-600\",\n                                            children: [\n                                                stats.retailCustomers,\n                                                \" retail, \",\n                                                stats.wholesaleCustomers,\n                                                \" wholesale\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: \"Retail Customers\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: stats.loading ? \"...\" : stats.retailCustomers\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-blue-600\",\n                                            children: \"Individual customers\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: \"Low Stock Items\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold \".concat(stats.lowStockProducts > 0 ? \"text-red-600\" : \"text-green-600\"),\n                                            children: stats.loading ? \"...\" : stats.lowStockProducts\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs \".concat(stats.lowStockProducts > 0 ? \"text-red-600\" : \"text-green-600\"),\n                                            children: stats.lowStockProducts > 0 ? \"Requires attention\" : \"All items well stocked\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_SalesMetricsSection__WEBPACK_IMPORTED_MODULE_6__.SalesMetricsSection, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_RevenueChartsSection__WEBPACK_IMPORTED_MODULE_7__.RevenueChartsSection, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_InventoryAlertsSection__WEBPACK_IMPORTED_MODULE_8__.InventoryAlertsSection, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            children: \"Recent Orders\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: \"Latest customer orders\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Order #1234\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 142,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"John Doe\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 143,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: \"$89.99\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 146,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-green-600\",\n                                                                children: \"Completed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 147,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Order #1235\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 152,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Jane Smith\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: \"$156.50\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-yellow-600\",\n                                                                children: \"Processing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 157,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            children: \"Quick Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: \"Common tasks and shortcuts\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                className: \"h-20 flex-col\",\n                                                onClick: ()=>window.open(\"/test-db\", \"_blank\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg mb-1\",\n                                                        children: \"\\uD83D\\uDDC4️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Test Database\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                className: \"h-20 flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg mb-1\",\n                                                        children: \"\\uD83D\\uDCE6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Manage Products\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                className: \"h-20 flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg mb-1\",\n                                                        children: \"\\uD83D\\uDC65\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"View Customers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                className: \"h-20 flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg mb-1\",\n                                                        children: \"\\uD83C\\uDFC3‍♂️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Coach Program\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"SgovsmiKK+PKqljzAFZvdFQHLr0=\", false, function() {\n    return [\n        _lib_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});