"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/award.js":
/*!********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/award.js ***!
  \********************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Award; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst Award = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Award\", [\n  [\"circle\", { cx: \"12\", cy: \"8\", r: \"6\", key: \"1vp47v\" }],\n  [\"path\", { d: \"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11\", key: \"em7aur\" }]\n]);\n\n\n//# sourceMappingURL=award.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbHVjaWRlLXJlYWN0QDAuMzAzLjBfcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvYXdhcmQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXNEOztBQUV0RCxjQUFjLGdFQUFnQjtBQUM5QixlQUFlLDBDQUEwQztBQUN6RCxhQUFhLDZEQUE2RDtBQUMxRTs7QUFFNEI7QUFDNUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9sdWNpZGUtcmVhY3RAMC4zMDMuMF9yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9hd2FyZC5qcz84MTQ1Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2UgbHVjaWRlLXJlYWN0IHYwLjMwMy4wIC0gSVNDXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgSVNDIGxpY2Vuc2UuXG4gKiBTZWUgdGhlIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqL1xuXG5pbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uLmpzJztcblxuY29uc3QgQXdhcmQgPSBjcmVhdGVMdWNpZGVJY29uKFwiQXdhcmRcIiwgW1xuICBbXCJjaXJjbGVcIiwgeyBjeDogXCIxMlwiLCBjeTogXCI4XCIsIHI6IFwiNlwiLCBrZXk6IFwiMXZwNDd2XCIgfV0sXG4gIFtcInBhdGhcIiwgeyBkOiBcIk0xNS40NzcgMTIuODkgMTcgMjJsLTUtMy01IDMgMS41MjMtOS4xMVwiLCBrZXk6IFwiZW03YXVyXCIgfV1cbl0pO1xuXG5leHBvcnQgeyBBd2FyZCBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1hd2FyZC5qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/award.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/user-check.js":
/*!*************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/user-check.js ***!
  \*************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UserCheck; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst UserCheck = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"UserCheck\", [\n  [\"path\", { d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\", key: \"1yyitq\" }],\n  [\"circle\", { cx: \"9\", cy: \"7\", r: \"4\", key: \"nufk8\" }],\n  [\"polyline\", { points: \"16 11 18 13 22 9\", key: \"1pwet4\" }]\n]);\n\n\n//# sourceMappingURL=user-check.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbHVjaWRlLXJlYWN0QDAuMzAzLjBfcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvdXNlci1jaGVjay5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFc0Q7O0FBRXRELGtCQUFrQixnRUFBZ0I7QUFDbEMsYUFBYSwrREFBK0Q7QUFDNUUsZUFBZSx3Q0FBd0M7QUFDdkQsaUJBQWlCLDJDQUEyQztBQUM1RDs7QUFFZ0M7QUFDaEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9sdWNpZGUtcmVhY3RAMC4zMDMuMF9yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy91c2VyLWNoZWNrLmpzP2UxMGEiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuMzAzLjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBVc2VyQ2hlY2sgPSBjcmVhdGVMdWNpZGVJY29uKFwiVXNlckNoZWNrXCIsIFtcbiAgW1wicGF0aFwiLCB7IGQ6IFwiTTE2IDIxdi0yYTQgNCAwIDAgMC00LTRINmE0IDQgMCAwIDAtNCA0djJcIiwga2V5OiBcIjF5eWl0cVwiIH1dLFxuICBbXCJjaXJjbGVcIiwgeyBjeDogXCI5XCIsIGN5OiBcIjdcIiwgcjogXCI0XCIsIGtleTogXCJudWZrOFwiIH1dLFxuICBbXCJwb2x5bGluZVwiLCB7IHBvaW50czogXCIxNiAxMSAxOCAxMyAyMiA5XCIsIGtleTogXCIxcHdldDRcIiB9XVxuXSk7XG5cbmV4cG9ydCB7IFVzZXJDaGVjayBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD11c2VyLWNoZWNrLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/user-check.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/users.js":
/*!********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/users.js ***!
  \********************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Users; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst Users = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Users\", [\n  [\"path\", { d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\", key: \"1yyitq\" }],\n  [\"circle\", { cx: \"9\", cy: \"7\", r: \"4\", key: \"nufk8\" }],\n  [\"path\", { d: \"M22 21v-2a4 4 0 0 0-3-3.87\", key: \"kshegd\" }],\n  [\"path\", { d: \"M16 3.13a4 4 0 0 1 0 7.75\", key: \"1da9ce\" }]\n]);\n\n\n//# sourceMappingURL=users.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbHVjaWRlLXJlYWN0QDAuMzAzLjBfcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvdXNlcnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXNEOztBQUV0RCxjQUFjLGdFQUFnQjtBQUM5QixhQUFhLCtEQUErRDtBQUM1RSxlQUFlLHdDQUF3QztBQUN2RCxhQUFhLGdEQUFnRDtBQUM3RCxhQUFhLCtDQUErQztBQUM1RDs7QUFFNEI7QUFDNUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9sdWNpZGUtcmVhY3RAMC4zMDMuMF9yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy91c2Vycy5qcz8yYzg5Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2UgbHVjaWRlLXJlYWN0IHYwLjMwMy4wIC0gSVNDXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgSVNDIGxpY2Vuc2UuXG4gKiBTZWUgdGhlIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqL1xuXG5pbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uLmpzJztcblxuY29uc3QgVXNlcnMgPSBjcmVhdGVMdWNpZGVJY29uKFwiVXNlcnNcIiwgW1xuICBbXCJwYXRoXCIsIHsgZDogXCJNMTYgMjF2LTJhNCA0IDAgMCAwLTQtNEg2YTQgNCAwIDAgMC00IDR2MlwiLCBrZXk6IFwiMXl5aXRxXCIgfV0sXG4gIFtcImNpcmNsZVwiLCB7IGN4OiBcIjlcIiwgY3k6IFwiN1wiLCByOiBcIjRcIiwga2V5OiBcIm51Zms4XCIgfV0sXG4gIFtcInBhdGhcIiwgeyBkOiBcIk0yMiAyMXYtMmE0IDQgMCAwIDAtMy0zLjg3XCIsIGtleTogXCJrc2hlZ2RcIiB9XSxcbiAgW1wicGF0aFwiLCB7IGQ6IFwiTTE2IDMuMTNhNCA0IDAgMCAxIDAgNy43NVwiLCBrZXk6IFwiMWRhOWNlXCIgfV1cbl0pO1xuXG5leHBvcnQgeyBVc2VycyBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD11c2Vycy5qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/users.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth-context */ \"(app-pages-browser)/./src/lib/auth-context.tsx\");\n/* harmony import */ var _nutripro_database__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @nutripro/database */ \"(app-pages-browser)/../../packages/database/src/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button!=!@nutripro/ui */ \"(app-pages-browser)/__barrel_optimize__?names=Button!=!../../packages/ui/src/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardContent,CardDescription,CardHeader,CardTitle!=!@nutripro/ui */ \"(app-pages-browser)/__barrel_optimize__?names=Card,CardContent,CardDescription,CardHeader,CardTitle!=!../../packages/ui/src/index.ts\");\n/* harmony import */ var _components_dashboard_SalesMetricsSection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/SalesMetricsSection */ \"(app-pages-browser)/./src/components/dashboard/SalesMetricsSection.tsx\");\n/* harmony import */ var _components_dashboard_RevenueChartsSection__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/RevenueChartsSection */ \"(app-pages-browser)/./src/components/dashboard/RevenueChartsSection.tsx\");\n/* harmony import */ var _components_dashboard_InventoryAlertsSection__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/dashboard/InventoryAlertsSection */ \"(app-pages-browser)/./src/components/dashboard/InventoryAlertsSection.tsx\");\n/* harmony import */ var _components_dashboard_CustomerAnalyticsSection__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/dashboard/CustomerAnalyticsSection */ \"(app-pages-browser)/./src/components/dashboard/CustomerAnalyticsSection.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const { user } = (0,_lib_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalProducts: 0,\n        lowStockProducts: 0,\n        totalCustomers: 0,\n        retailCustomers: 0,\n        wholesaleCustomers: 0,\n        loading: true\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadStats = async ()=>{\n            try {\n                const productQueries = new _nutripro_database__WEBPACK_IMPORTED_MODULE_3__.ProductQueries();\n                const customerQueries = new _nutripro_database__WEBPACK_IMPORTED_MODULE_3__.CustomerQueries();\n                const [products, lowStock, customerStats] = await Promise.all([\n                    productQueries.getAll(),\n                    productQueries.getLowStock(10),\n                    customerQueries.getStats()\n                ]);\n                setStats({\n                    totalProducts: (products === null || products === void 0 ? void 0 : products.length) || 0,\n                    lowStockProducts: (lowStock === null || lowStock === void 0 ? void 0 : lowStock.length) || 0,\n                    totalCustomers: customerStats.total,\n                    retailCustomers: customerStats.retail,\n                    wholesaleCustomers: customerStats.wholesale,\n                    loading: false\n                });\n            } catch (error) {\n                console.error(\"Failed to load dashboard stats:\", error);\n                setStats((prev)=>({\n                        ...prev,\n                        loading: false\n                    }));\n            }\n        };\n        loadStats();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4 md:p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 md:mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl md:text-3xl font-bold text-gray-900\",\n                            children: \"Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-1\",\n                            children: [\n                                \"Welcome back, \",\n                                (user === null || user === void 0 ? void 0 : user.email) || \"Demo User\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6 md:mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: \"Total Products\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: stats.loading ? \"...\" : stats.totalProducts\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-blue-600\",\n                                            children: \"Active inventory items\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: \"Total Customers\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: stats.loading ? \"...\" : stats.totalCustomers\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-green-600\",\n                                            children: [\n                                                stats.retailCustomers,\n                                                \" retail, \",\n                                                stats.wholesaleCustomers,\n                                                \" wholesale\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: \"Retail Customers\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: stats.loading ? \"...\" : stats.retailCustomers\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-blue-600\",\n                                            children: \"Individual customers\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: \"Low Stock Items\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold \".concat(stats.lowStockProducts > 0 ? \"text-red-600\" : \"text-green-600\"),\n                                            children: stats.loading ? \"...\" : stats.lowStockProducts\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs \".concat(stats.lowStockProducts > 0 ? \"text-red-600\" : \"text-green-600\"),\n                                            children: stats.lowStockProducts > 0 ? \"Requires attention\" : \"All items well stocked\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_SalesMetricsSection__WEBPACK_IMPORTED_MODULE_6__.SalesMetricsSection, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_RevenueChartsSection__WEBPACK_IMPORTED_MODULE_7__.RevenueChartsSection, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_InventoryAlertsSection__WEBPACK_IMPORTED_MODULE_8__.InventoryAlertsSection, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_CustomerAnalyticsSection__WEBPACK_IMPORTED_MODULE_9__.CustomerAnalyticsSection, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            children: \"Recent Orders\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: \"Latest customer orders\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Order #1234\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 145,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"John Doe\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 146,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: \"$89.99\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 149,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-green-600\",\n                                                                children: \"Completed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 150,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Order #1235\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Jane Smith\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: \"$156.50\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 159,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-yellow-600\",\n                                                                children: \"Processing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 160,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            children: \"Quick Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: \"Common tasks and shortcuts\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                className: \"h-20 flex-col\",\n                                                onClick: ()=>window.open(\"/test-db\", \"_blank\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg mb-1\",\n                                                        children: \"\\uD83D\\uDDC4️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Test Database\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                className: \"h-20 flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg mb-1\",\n                                                        children: \"\\uD83D\\uDCE6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Manage Products\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                className: \"h-20 flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg mb-1\",\n                                                        children: \"\\uD83D\\uDC65\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"View Customers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                className: \"h-20 flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg mb-1\",\n                                                        children: \"\\uD83C\\uDFC3‍♂️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Coach Program\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"SgovsmiKK+PKqljzAFZvdFQHLr0=\", false, function() {\n    return [\n        _lib_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/CustomerAnalyticsSection.tsx":
/*!***************************************************************!*\
  !*** ./src/components/dashboard/CustomerAnalyticsSection.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomerAnalyticsSection: function() { return /* binding */ CustomerAnalyticsSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardContent,CardDescription,CardHeader,CardTitle!=!@nutripro/ui */ \"(app-pages-browser)/__barrel_optimize__?names=Card,CardContent,CardDescription,CardHeader,CardTitle!=!../../packages/ui/src/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Badge_nutripro_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Badge!=!@nutripro/ui */ \"(app-pages-browser)/__barrel_optimize__?names=Badge!=!../../packages/ui/src/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Award_TrendingUp_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,TrendingUp,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_TrendingUp_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,TrendingUp,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_Award_TrendingUp_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,TrendingUp,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_TrendingUp_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,TrendingUp,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _nutripro_database__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @nutripro/database */ \"(app-pages-browser)/../../packages/database/src/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/component/Cell.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/chart/AreaChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Cell,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/cartesian/Area.js\");\n/* __next_internal_client_entry_do_not_use__ CustomerAnalyticsSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Mock data generators\nconst generateCustomerMetrics = (customers)=>{\n    const totalCustomers = (customers === null || customers === void 0 ? void 0 : customers.length) || 0;\n    const loyaltyMembers = (customers === null || customers === void 0 ? void 0 : customers.filter((c)=>c.loyalty_points > 0).length) || 0;\n    const premiumMembers = (customers === null || customers === void 0 ? void 0 : customers.filter((c)=>c.membership_tier === \"premium\").length) || 0;\n    const totalLoyaltyPoints = (customers === null || customers === void 0 ? void 0 : customers.reduce((sum, c)=>sum + (c.loyalty_points || 0), 0)) || 0;\n    return {\n        totalCustomers,\n        newCustomersThisMonth: Math.floor(totalCustomers * 0.15),\n        activeCustomers: Math.floor(totalCustomers * 0.75),\n        loyaltyMembers,\n        premiumMembers,\n        averageLoyaltyPoints: loyaltyMembers > 0 ? Math.round(totalLoyaltyPoints / loyaltyMembers) : 0,\n        totalLoyaltyPoints,\n        customerRetentionRate: 85.5 // Mock retention rate\n    };\n};\nconst generateCustomerSegmentData = ()=>{\n    return [\n        {\n            segment: \"Regular Retail\",\n            count: 45,\n            percentage: 60,\n            color: \"#3B82F6\"\n        },\n        {\n            segment: \"Premium Members\",\n            count: 15,\n            percentage: 20,\n            color: \"#10B981\"\n        },\n        {\n            segment: \"Wholesale\",\n            count: 10,\n            percentage: 13,\n            color: \"#F59E0B\"\n        },\n        {\n            segment: \"Inactive\",\n            count: 5,\n            percentage: 7,\n            color: \"#EF4444\"\n        }\n    ];\n};\nconst generateLoyaltyTrendData = ()=>{\n    const months = [\n        \"Jan\",\n        \"Feb\",\n        \"Mar\",\n        \"Apr\",\n        \"May\",\n        \"Jun\"\n    ];\n    return months.map((month)=>({\n            month,\n            newMembers: Math.floor(Math.random() * 15) + 5,\n            totalPoints: Math.floor(Math.random() * 5000) + 2000,\n            redemptions: Math.floor(Math.random() * 800) + 200\n        }));\n};\nfunction CustomerAnalyticsSection() {\n    _s();\n    const [metrics, setMetrics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalCustomers: 0,\n        newCustomersThisMonth: 0,\n        activeCustomers: 0,\n        loyaltyMembers: 0,\n        premiumMembers: 0,\n        averageLoyaltyPoints: 0,\n        totalLoyaltyPoints: 0,\n        customerRetentionRate: 0\n    });\n    const [segmentData, setSegmentData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loyaltyTrendData, setLoyaltyTrendData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadCustomerData = async ()=>{\n            try {\n                const customerQueries = new _nutripro_database__WEBPACK_IMPORTED_MODULE_4__.CustomerQueries();\n                const customers = await customerQueries.getAll();\n                setMetrics(generateCustomerMetrics(customers));\n                setSegmentData(generateCustomerSegmentData());\n                setLoyaltyTrendData(generateLoyaltyTrendData());\n            } catch (error) {\n                console.error(\"Failed to load customer data:\", error);\n                // Use mock data on error\n                setSegmentData(generateCustomerSegmentData());\n                setLoyaltyTrendData(generateLoyaltyTrendData());\n            } finally{\n                setLoading(false);\n            }\n        };\n        loadCustomerData();\n    }, []);\n    const MetricCard = (param)=>{\n        let { title, value, subtitle, icon: Icon, trend, prefix = \"\", suffix = \"\" } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-sm font-medium text-gray-600\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            className: \"h-4 w-4 text-muted-foreground\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-2xl font-bold\",\n                            children: loading ? \"...\" : \"\".concat(prefix).concat(value).concat(suffix)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: subtitle\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 11\n                                }, this),\n                                trend && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_nutripro_ui__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                    variant: \"outline\",\n                                    className: trend === \"up\" ? \"text-green-600 border-green-200\" : trend === \"down\" ? \"text-red-600 border-red-200\" : \"text-gray-600 border-gray-200\",\n                                    children: trend === \"up\" ? \"↗\" : trend === \"down\" ? \"↘\" : \"→\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n            lineNumber: 142,\n            columnNumber: 5\n        }, this);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-6 md:mb-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900\",\n                            children: \"Customer Analytics\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Customer behavior, loyalty metrics, and segmentation insights\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6\",\n                    children: [\n                        1,\n                        2,\n                        3,\n                        4\n                    ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-gray-200 rounded animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-64 bg-gray-100 rounded animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n            lineNumber: 171,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-6 md:mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-900\",\n                        children: \"Customer Analytics\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Customer behavior, loyalty metrics, and segmentation insights\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                        title: \"Total Customers\",\n                        value: metrics.totalCustomers,\n                        subtitle: \"\".concat(metrics.newCustomersThisMonth, \" new this month\"),\n                        icon: _barrel_optimize_names_Award_TrendingUp_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                        trend: \"up\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                        title: \"Active Customers\",\n                        value: metrics.activeCustomers,\n                        subtitle: \"\".concat(Math.round(metrics.activeCustomers / metrics.totalCustomers * 100), \"% of total\"),\n                        icon: _barrel_optimize_names_Award_TrendingUp_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                        trend: \"up\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                        title: \"Loyalty Members\",\n                        value: metrics.loyaltyMembers,\n                        subtitle: \"\".concat(metrics.premiumMembers, \" premium members\"),\n                        icon: _barrel_optimize_names_Award_TrendingUp_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                        trend: \"up\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                        title: \"Retention Rate\",\n                        value: metrics.customerRetentionRate,\n                        subtitle: \"last 12 months\",\n                        icon: _barrel_optimize_names_Award_TrendingUp_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                        suffix: \"%\",\n                        trend: \"up\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        children: \"Customer Segmentation\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"Distribution of customers by type and status\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 250,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.PieChart, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Pie, {\n                                                    data: segmentData,\n                                                    cx: \"50%\",\n                                                    cy: \"50%\",\n                                                    innerRadius: 60,\n                                                    outerRadius: 100,\n                                                    paddingAngle: 5,\n                                                    dataKey: \"count\",\n                                                    children: segmentData.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.Cell, {\n                                                            fill: entry.color\n                                                        }, \"cell-\".concat(index), false, {\n                                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                    formatter: (value, name)=>[\n                                                            \"\".concat(value, \" customers\"),\n                                                            \"Count\"\n                                                        ]\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 space-y-2\",\n                                        children: segmentData.map((segment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 rounded-full\",\n                                                                style: {\n                                                                    backgroundColor: segment.color\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: segment.segment\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: segment.count\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-500 ml-1\",\n                                                                children: [\n                                                                    \"(\",\n                                                                    segment.percentage,\n                                                                    \"%)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        children: \"Loyalty Program Trends\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"Monthly loyalty member growth and point activity\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.ResponsiveContainer, {\n                                    width: \"100%\",\n                                    height: 250,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.AreaChart, {\n                                        data: loyaltyTrendData,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__.CartesianGrid, {\n                                                strokeDasharray: \"3 3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__.XAxis, {\n                                                dataKey: \"month\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_17__.YAxis, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_18__.Area, {\n                                                type: \"monotone\",\n                                                dataKey: \"newMembers\",\n                                                stackId: \"1\",\n                                                stroke: \"#3B82F6\",\n                                                fill: \"#3B82F6\",\n                                                fillOpacity: 0.6,\n                                                name: \"New Members\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Cell_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_18__.Area, {\n                                                type: \"monotone\",\n                                                dataKey: \"redemptions\",\n                                                stackId: \"2\",\n                                                stroke: \"#10B981\",\n                                                fill: \"#10B981\",\n                                                fillOpacity: 0.6,\n                                                name: \"Point Redemptions\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CustomerAnalyticsSection.tsx\",\n        lineNumber: 193,\n        columnNumber: 5\n    }, this);\n}\n_s(CustomerAnalyticsSection, \"nCXsyefoudYmQY/sIKBUYxBsCaU=\");\n_c = CustomerAnalyticsSection;\nvar _c;\n$RefreshReg$(_c, \"CustomerAnalyticsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/CustomerAnalyticsSection.tsx\n"));

/***/ })

});