"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trophy.js":
/*!*********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trophy.js ***!
  \*********************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Trophy; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst Trophy = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Trophy\", [\n  [\"path\", { d: \"M6 9H4.5a2.5 2.5 0 0 1 0-5H6\", key: \"17hqa7\" }],\n  [\"path\", { d: \"M18 9h1.5a2.5 2.5 0 0 0 0-5H18\", key: \"lmptdp\" }],\n  [\"path\", { d: \"M4 22h16\", key: \"57wxv0\" }],\n  [\"path\", { d: \"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22\", key: \"1nw9bq\" }],\n  [\"path\", { d: \"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22\", key: \"1np0yb\" }],\n  [\"path\", { d: \"M18 2H6v7a6 6 0 0 0 12 0V2Z\", key: \"u46fv3\" }]\n]);\n\n\n//# sourceMappingURL=trophy.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbHVjaWRlLXJlYWN0QDAuMzAzLjBfcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvdHJvcGh5LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVzRDs7QUFFdEQsZUFBZSxnRUFBZ0I7QUFDL0IsYUFBYSxrREFBa0Q7QUFDL0QsYUFBYSxvREFBb0Q7QUFDakUsYUFBYSw4QkFBOEI7QUFDM0MsYUFBYSxnRkFBZ0Y7QUFDN0YsYUFBYSxpRkFBaUY7QUFDOUYsYUFBYSxpREFBaUQ7QUFDOUQ7O0FBRTZCO0FBQzdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbHVjaWRlLXJlYWN0QDAuMzAzLjBfcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvdHJvcGh5LmpzP2M4ODAiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuMzAzLjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBUcm9waHkgPSBjcmVhdGVMdWNpZGVJY29uKFwiVHJvcGh5XCIsIFtcbiAgW1wicGF0aFwiLCB7IGQ6IFwiTTYgOUg0LjVhMi41IDIuNSAwIDAgMSAwLTVINlwiLCBrZXk6IFwiMTdocWE3XCIgfV0sXG4gIFtcInBhdGhcIiwgeyBkOiBcIk0xOCA5aDEuNWEyLjUgMi41IDAgMCAwIDAtNUgxOFwiLCBrZXk6IFwibG1wdGRwXCIgfV0sXG4gIFtcInBhdGhcIiwgeyBkOiBcIk00IDIyaDE2XCIsIGtleTogXCI1N3d4djBcIiB9XSxcbiAgW1wicGF0aFwiLCB7IGQ6IFwiTTEwIDE0LjY2VjE3YzAgLjU1LS40Ny45OC0uOTcgMS4yMUM3Ljg1IDE4Ljc1IDcgMjAuMjQgNyAyMlwiLCBrZXk6IFwiMW53OWJxXCIgfV0sXG4gIFtcInBhdGhcIiwgeyBkOiBcIk0xNCAxNC42NlYxN2MwIC41NS40Ny45OC45NyAxLjIxQzE2LjE1IDE4Ljc1IDE3IDIwLjI0IDE3IDIyXCIsIGtleTogXCIxbnAweWJcIiB9XSxcbiAgW1wicGF0aFwiLCB7IGQ6IFwiTTE4IDJINnY3YTYgNiAwIDAgMCAxMiAwVjJaXCIsIGtleTogXCJ1NDZmdjNcIiB9XVxuXSk7XG5cbmV4cG9ydCB7IFRyb3BoeSBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD10cm9waHkuanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trophy.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth-context */ \"(app-pages-browser)/./src/lib/auth-context.tsx\");\n/* harmony import */ var _nutripro_database__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @nutripro/database */ \"(app-pages-browser)/../../packages/database/src/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button!=!@nutripro/ui */ \"(app-pages-browser)/__barrel_optimize__?names=Button!=!../../packages/ui/src/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardContent,CardDescription,CardHeader,CardTitle!=!@nutripro/ui */ \"(app-pages-browser)/__barrel_optimize__?names=Card,CardContent,CardDescription,CardHeader,CardTitle!=!../../packages/ui/src/index.ts\");\n/* harmony import */ var _components_dashboard_SalesMetricsSection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/SalesMetricsSection */ \"(app-pages-browser)/./src/components/dashboard/SalesMetricsSection.tsx\");\n/* harmony import */ var _components_dashboard_RevenueChartsSection__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/RevenueChartsSection */ \"(app-pages-browser)/./src/components/dashboard/RevenueChartsSection.tsx\");\n/* harmony import */ var _components_dashboard_InventoryAlertsSection__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/dashboard/InventoryAlertsSection */ \"(app-pages-browser)/./src/components/dashboard/InventoryAlertsSection.tsx\");\n/* harmony import */ var _components_dashboard_CustomerAnalyticsSection__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/dashboard/CustomerAnalyticsSection */ \"(app-pages-browser)/./src/components/dashboard/CustomerAnalyticsSection.tsx\");\n/* harmony import */ var _components_dashboard_CoachPerformanceSection__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/dashboard/CoachPerformanceSection */ \"(app-pages-browser)/./src/components/dashboard/CoachPerformanceSection.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const { user } = (0,_lib_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalProducts: 0,\n        lowStockProducts: 0,\n        totalCustomers: 0,\n        retailCustomers: 0,\n        wholesaleCustomers: 0,\n        loading: true\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadStats = async ()=>{\n            try {\n                const productQueries = new _nutripro_database__WEBPACK_IMPORTED_MODULE_3__.ProductQueries();\n                const customerQueries = new _nutripro_database__WEBPACK_IMPORTED_MODULE_3__.CustomerQueries();\n                const [products, lowStock, customerStats] = await Promise.all([\n                    productQueries.getAll(),\n                    productQueries.getLowStock(10),\n                    customerQueries.getStats()\n                ]);\n                setStats({\n                    totalProducts: (products === null || products === void 0 ? void 0 : products.length) || 0,\n                    lowStockProducts: (lowStock === null || lowStock === void 0 ? void 0 : lowStock.length) || 0,\n                    totalCustomers: customerStats.total,\n                    retailCustomers: customerStats.retail,\n                    wholesaleCustomers: customerStats.wholesale,\n                    loading: false\n                });\n            } catch (error) {\n                console.error(\"Failed to load dashboard stats:\", error);\n                setStats((prev)=>({\n                        ...prev,\n                        loading: false\n                    }));\n            }\n        };\n        loadStats();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4 md:p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 md:mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl md:text-3xl font-bold text-gray-900\",\n                            children: \"Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-1\",\n                            children: [\n                                \"Welcome back, \",\n                                (user === null || user === void 0 ? void 0 : user.email) || \"Demo User\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6 md:mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: \"Total Products\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: stats.loading ? \"...\" : stats.totalProducts\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-blue-600\",\n                                            children: \"Active inventory items\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: \"Total Customers\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: stats.loading ? \"...\" : stats.totalCustomers\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-green-600\",\n                                            children: [\n                                                stats.retailCustomers,\n                                                \" retail, \",\n                                                stats.wholesaleCustomers,\n                                                \" wholesale\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: \"Retail Customers\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: stats.loading ? \"...\" : stats.retailCustomers\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-blue-600\",\n                                            children: \"Individual customers\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: \"Low Stock Items\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold \".concat(stats.lowStockProducts > 0 ? \"text-red-600\" : \"text-green-600\"),\n                                            children: stats.loading ? \"...\" : stats.lowStockProducts\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs \".concat(stats.lowStockProducts > 0 ? \"text-red-600\" : \"text-green-600\"),\n                                            children: stats.lowStockProducts > 0 ? \"Requires attention\" : \"All items well stocked\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_SalesMetricsSection__WEBPACK_IMPORTED_MODULE_6__.SalesMetricsSection, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_RevenueChartsSection__WEBPACK_IMPORTED_MODULE_7__.RevenueChartsSection, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_InventoryAlertsSection__WEBPACK_IMPORTED_MODULE_8__.InventoryAlertsSection, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_CustomerAnalyticsSection__WEBPACK_IMPORTED_MODULE_9__.CustomerAnalyticsSection, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_CoachPerformanceSection__WEBPACK_IMPORTED_MODULE_10__.CoachPerformanceSection, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            children: \"Recent Orders\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: \"Latest customer orders\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Order #1234\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 149,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"John Doe\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 150,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: \"$89.99\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-green-600\",\n                                                                children: \"Completed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 154,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Order #1235\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 159,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Jane Smith\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 160,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: \"$156.50\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-yellow-600\",\n                                                                children: \"Processing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 164,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            children: \"Quick Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: \"Common tasks and shortcuts\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                className: \"h-20 flex-col\",\n                                                onClick: ()=>window.open(\"/test-db\", \"_blank\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg mb-1\",\n                                                        children: \"\\uD83D\\uDDC4️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Test Database\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                className: \"h-20 flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg mb-1\",\n                                                        children: \"\\uD83D\\uDCE6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Manage Products\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                className: \"h-20 flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg mb-1\",\n                                                        children: \"\\uD83D\\uDC65\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"View Customers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                className: \"h-20 flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg mb-1\",\n                                                        children: \"\\uD83C\\uDFC3‍♂️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Coach Program\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"SgovsmiKK+PKqljzAFZvdFQHLr0=\", false, function() {\n    return [\n        _lib_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/CoachPerformanceSection.tsx":
/*!**************************************************************!*\
  !*** ./src/components/dashboard/CoachPerformanceSection.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CoachPerformanceSection: function() { return /* binding */ CoachPerformanceSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardContent,CardDescription,CardHeader,CardTitle!=!@nutripro/ui */ \"(app-pages-browser)/__barrel_optimize__?names=Card,CardContent,CardDescription,CardHeader,CardTitle!=!../../packages/ui/src/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Badge_nutripro_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Badge!=!@nutripro/ui */ \"(app-pages-browser)/__barrel_optimize__?names=Badge!=!../../packages/ui/src/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Award_DollarSign_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,DollarSign,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Award_DollarSign_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,DollarSign,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_DollarSign_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,DollarSign,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_DollarSign_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,DollarSign,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/chart/AreaChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/cartesian/Area.js\");\n/* __next_internal_client_entry_do_not_use__ CoachPerformanceSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// Mock data generators\nconst generateCoachPerformanceData = ()=>{\n    const coaches = [\n        {\n            name: \"Maria Rodriguez\",\n            baseReferrals: 45,\n            baseSales: 12500\n        },\n        {\n            name: \"James Wilson\",\n            baseReferrals: 38,\n            baseSales: 10800\n        },\n        {\n            name: \"Sarah Chen\",\n            baseReferrals: 32,\n            baseSales: 9200\n        },\n        {\n            name: \"Michael Brown\",\n            baseReferrals: 28,\n            baseSales: 8100\n        }\n    ];\n    return coaches.map((coach, index)=>{\n        const monthlyVariation = 0.8 + Math.random() * 0.4 // 80-120%\n        ;\n        const monthlyReferrals = Math.round(coach.baseReferrals * 0.15 * monthlyVariation);\n        const monthlySales = Math.round(coach.baseSales * 0.15 * monthlyVariation);\n        const commissionRate = 0.05 + index * 0.01 // 5-8% commission rate\n        ;\n        return {\n            id: \"coach-\".concat(index + 1),\n            name: coach.name,\n            totalReferrals: coach.baseReferrals,\n            monthlyReferrals,\n            totalSales: coach.baseSales,\n            monthlySales,\n            commissionEarned: Math.round(coach.baseSales * commissionRate),\n            monthlyCommission: Math.round(monthlySales * commissionRate),\n            referralRate: commissionRate * 100,\n            rank: index + 1,\n            trend: Math.random() > 0.3 ? \"up\" : Math.random() > 0.5 ? \"stable\" : \"down\"\n        };\n    });\n};\nconst generateMonthlyPerformanceData = ()=>{\n    const months = [\n        \"Jan\",\n        \"Feb\",\n        \"Mar\",\n        \"Apr\",\n        \"May\",\n        \"Jun\"\n    ];\n    return months.map((month)=>({\n            month,\n            totalSales: Math.floor(Math.random() * 15000) + 25000,\n            totalCommissions: Math.floor(Math.random() * 800) + 1200,\n            newReferrals: Math.floor(Math.random() * 20) + 15\n        }));\n};\nfunction CoachPerformanceSection() {\n    _s();\n    const [coachData, setCoachData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [monthlyData, setMonthlyData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simulate API call delay\n        const timer = setTimeout(()=>{\n            setCoachData(generateCoachPerformanceData());\n            setMonthlyData(generateMonthlyPerformanceData());\n            setLoading(false);\n        }, 1500);\n        return ()=>clearTimeout(timer);\n    }, []);\n    const getTrendIcon = (trend)=>{\n        switch(trend){\n            case \"up\":\n                return \"↗️\";\n            case \"down\":\n                return \"↘️\";\n            default:\n                return \"➡️\";\n        }\n    };\n    const getTrendColor = (trend)=>{\n        switch(trend){\n            case \"up\":\n                return \"text-green-600 bg-green-50 border-green-200\";\n            case \"down\":\n                return \"text-red-600 bg-red-50 border-red-200\";\n            default:\n                return \"text-gray-600 bg-gray-50 border-gray-200\";\n        }\n    };\n    const getRankBadgeColor = (rank)=>{\n        switch(rank){\n            case 1:\n                return \"bg-yellow-100 text-yellow-800 border-yellow-300\";\n            case 2:\n                return \"bg-gray-100 text-gray-800 border-gray-300\";\n            case 3:\n                return \"bg-orange-100 text-orange-800 border-orange-300\";\n            default:\n                return \"bg-blue-100 text-blue-800 border-blue-300\";\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-6 md:mb-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900\",\n                            children: \"Coach Performance\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Referral tracking, commission analytics, and performance metrics\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6\",\n                    children: [\n                        1,\n                        2,\n                        3\n                    ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-gray-200 rounded animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-64 bg-gray-100 rounded animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this);\n    }\n    const totalMonthlySales = coachData.reduce((sum, coach)=>sum + coach.monthlySales, 0);\n    const totalMonthlyCommissions = coachData.reduce((sum, coach)=>sum + coach.monthlyCommission, 0);\n    const totalMonthlyReferrals = coachData.reduce((sum, coach)=>sum + coach.monthlyReferrals, 0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-6 md:mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-900\",\n                        children: \"Coach Performance\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Referral tracking, commission analytics, and performance metrics\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-3 gap-4 md:gap-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-sm font-medium text-gray-600 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_DollarSign_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Monthly Coach Sales\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: [\n                                            \"AWG \",\n                                            totalMonthlySales.toLocaleString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-green-600\",\n                                        children: \"+12% from last month\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-sm font-medium text-gray-600 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_DollarSign_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Total Commissions\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: [\n                                            \"AWG \",\n                                            totalMonthlyCommissions.toLocaleString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-600\",\n                                        children: \"This month\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-sm font-medium text-gray-600 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_DollarSign_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"New Referrals\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: totalMonthlyReferrals\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-green-600\",\n                                        children: \"This month\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_DollarSign_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-5 w-5 text-yellow-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Coach Leaderboard\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"Top performing coaches this month\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: coachData.map((coach)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_nutripro_ui__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                            className: getRankBadgeColor(coach.rank),\n                                                            children: [\n                                                                \"#\",\n                                                                coach.rank\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-sm\",\n                                                                    children: coach.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                                                    lineNumber: 219,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        coach.monthlyReferrals,\n                                                                        \" referrals • \",\n                                                                        coach.referralRate,\n                                                                        \"% rate\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                                                    lineNumber: 220,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-sm\",\n                                                            children: [\n                                                                \"AWG \",\n                                                                coach.monthlySales.toLocaleString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_nutripro_ui__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: getTrendColor(coach.trend),\n                                                                    children: getTrendIcon(coach.trend)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                                                    lineNumber: 228,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        \"AWG \",\n                                                                        coach.monthlyCommission\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                                                    lineNumber: 231,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, coach.id, true, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        children: \"Monthly Performance Trends\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"Coach program sales and commission trends\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.ResponsiveContainer, {\n                                    width: \"100%\",\n                                    height: 250,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.AreaChart, {\n                                        data: monthlyData,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.CartesianGrid, {\n                                                strokeDasharray: \"3 3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.XAxis, {\n                                                dataKey: \"month\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.YAxis, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                formatter: (value, name)=>[\n                                                        name === \"totalSales\" ? \"AWG \".concat(value.toLocaleString()) : name === \"totalCommissions\" ? \"AWG \".concat(value) : value,\n                                                        name === \"totalSales\" ? \"Sales\" : name === \"totalCommissions\" ? \"Commissions\" : \"Referrals\"\n                                                    ]\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.Area, {\n                                                type: \"monotone\",\n                                                dataKey: \"totalSales\",\n                                                stackId: \"1\",\n                                                stroke: \"#3B82F6\",\n                                                fill: \"#3B82F6\",\n                                                fillOpacity: 0.6\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.Area, {\n                                                type: \"monotone\",\n                                                dataKey: \"totalCommissions\",\n                                                stackId: \"2\",\n                                                stroke: \"#10B981\",\n                                                fill: \"#10B981\",\n                                                fillOpacity: 0.6\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/CoachPerformanceSection.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n_s(CoachPerformanceSection, \"7W4elvMRkjOssS/WC7j23P9bU5g=\");\n_c = CoachPerformanceSection;\nvar _c;\n$RefreshReg$(_c, \"CoachPerformanceSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9Db2FjaFBlcmZvcm1hbmNlU2VjdGlvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUUyQztBQUM2QztBQUNwRDtBQUM2QztBQWFoRTtBQXVCakIsdUJBQXVCO0FBQ3ZCLE1BQU1tQiwrQkFBK0I7SUFDbkMsTUFBTUMsVUFBVTtRQUNkO1lBQUVDLE1BQU07WUFBbUJDLGVBQWU7WUFBSUMsV0FBVztRQUFNO1FBQy9EO1lBQUVGLE1BQU07WUFBZ0JDLGVBQWU7WUFBSUMsV0FBVztRQUFNO1FBQzVEO1lBQUVGLE1BQU07WUFBY0MsZUFBZTtZQUFJQyxXQUFXO1FBQUs7UUFDekQ7WUFBRUYsTUFBTTtZQUFpQkMsZUFBZTtZQUFJQyxXQUFXO1FBQUs7S0FDN0Q7SUFFRCxPQUFPSCxRQUFRSSxHQUFHLENBQUMsQ0FBQ0MsT0FBT0M7UUFDekIsTUFBTUMsbUJBQW1CLE1BQU1DLEtBQUtDLE1BQU0sS0FBSyxJQUFJLFVBQVU7O1FBQzdELE1BQU1DLG1CQUFtQkYsS0FBS0csS0FBSyxDQUFDTixNQUFNSCxhQUFhLEdBQUcsT0FBT0s7UUFDakUsTUFBTUssZUFBZUosS0FBS0csS0FBSyxDQUFDTixNQUFNRixTQUFTLEdBQUcsT0FBT0k7UUFDekQsTUFBTU0saUJBQWlCLE9BQVFQLFFBQVEsS0FBTSx1QkFBdUI7O1FBRXBFLE9BQU87WUFDTFEsSUFBSSxTQUFtQixPQUFWUixRQUFRO1lBQ3JCTCxNQUFNSSxNQUFNSixJQUFJO1lBQ2hCYyxnQkFBZ0JWLE1BQU1ILGFBQWE7WUFDbkNRO1lBQ0FNLFlBQVlYLE1BQU1GLFNBQVM7WUFDM0JTO1lBQ0FLLGtCQUFrQlQsS0FBS0csS0FBSyxDQUFDTixNQUFNRixTQUFTLEdBQUdVO1lBQy9DSyxtQkFBbUJWLEtBQUtHLEtBQUssQ0FBQ0MsZUFBZUM7WUFDN0NNLGNBQWNOLGlCQUFpQjtZQUMvQk8sTUFBTWQsUUFBUTtZQUNkZSxPQUFPYixLQUFLQyxNQUFNLEtBQUssTUFBTSxPQUFPRCxLQUFLQyxNQUFNLEtBQUssTUFBTSxXQUFXO1FBQ3ZFO0lBQ0Y7QUFDRjtBQUVBLE1BQU1hLGlDQUFpQztJQUNyQyxNQUFNQyxTQUFTO1FBQUM7UUFBTztRQUFPO1FBQU87UUFBTztRQUFPO0tBQU07SUFDekQsT0FBT0EsT0FBT25CLEdBQUcsQ0FBQ29CLENBQUFBLFFBQVU7WUFDMUJBO1lBQ0FSLFlBQVlSLEtBQUtpQixLQUFLLENBQUNqQixLQUFLQyxNQUFNLEtBQUssU0FBUztZQUNoRGlCLGtCQUFrQmxCLEtBQUtpQixLQUFLLENBQUNqQixLQUFLQyxNQUFNLEtBQUssT0FBTztZQUNwRGtCLGNBQWNuQixLQUFLaUIsS0FBSyxDQUFDakIsS0FBS0MsTUFBTSxLQUFLLE1BQU07UUFDakQ7QUFDRjtBQUVPLFNBQVNtQjs7SUFDZCxNQUFNLENBQUNDLFdBQVdDLGFBQWEsR0FBR2pELCtDQUFRQSxDQUFxQixFQUFFO0lBQ2pFLE1BQU0sQ0FBQ2tELGFBQWFDLGVBQWUsR0FBR25ELCtDQUFRQSxDQUEyQixFQUFFO0lBQzNFLE1BQU0sQ0FBQ29ELFNBQVNDLFdBQVcsR0FBR3JELCtDQUFRQSxDQUFDO0lBRXZDRCxnREFBU0EsQ0FBQztRQUNSLDBCQUEwQjtRQUMxQixNQUFNdUQsUUFBUUMsV0FBVztZQUN2Qk4sYUFBYS9CO1lBQ2JpQyxlQUFlVjtZQUNmWSxXQUFXO1FBQ2IsR0FBRztRQUVILE9BQU8sSUFBTUcsYUFBYUY7SUFDNUIsR0FBRyxFQUFFO0lBRUwsTUFBTUcsZUFBZSxDQUFDakI7UUFDcEIsT0FBUUE7WUFDTixLQUFLO2dCQUFNLE9BQU87WUFDbEIsS0FBSztnQkFBUSxPQUFPO1lBQ3BCO2dCQUFTLE9BQU87UUFDbEI7SUFDRjtJQUVBLE1BQU1rQixnQkFBZ0IsQ0FBQ2xCO1FBQ3JCLE9BQVFBO1lBQ04sS0FBSztnQkFBTSxPQUFPO1lBQ2xCLEtBQUs7Z0JBQVEsT0FBTztZQUNwQjtnQkFBUyxPQUFPO1FBQ2xCO0lBQ0Y7SUFFQSxNQUFNbUIsb0JBQW9CLENBQUNwQjtRQUN6QixPQUFRQTtZQUNOLEtBQUs7Z0JBQUcsT0FBTztZQUNmLEtBQUs7Z0JBQUcsT0FBTztZQUNmLEtBQUs7Z0JBQUcsT0FBTztZQUNmO2dCQUFTLE9BQU87UUFDbEI7SUFDRjtJQUVBLElBQUlhLFNBQVM7UUFDWCxxQkFDRSw4REFBQ1E7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0M7NEJBQUdELFdBQVU7c0NBQXNDOzs7Ozs7c0NBQ3BELDhEQUFDRTs0QkFBRUYsV0FBVTtzQ0FBZ0I7Ozs7Ozs7Ozs7Ozs4QkFFL0IsOERBQUNEO29CQUFJQyxXQUFVOzhCQUNaO3dCQUFDO3dCQUFHO3dCQUFHO3FCQUFFLENBQUN0QyxHQUFHLENBQUMsQ0FBQ3lDLGtCQUNkLDhEQUFDL0QsMEhBQUlBOzs4Q0FDSCw4REFBQ0csZ0lBQVVBOzhDQUNULDRFQUFDd0Q7d0NBQUlDLFdBQVU7Ozs7Ozs7Ozs7OzhDQUVqQiw4REFBQzNELGlJQUFXQTs4Q0FDViw0RUFBQzBEO3dDQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7MkJBTFJHOzs7Ozs7Ozs7Ozs7Ozs7O0lBWXJCO0lBRUEsTUFBTUMsb0JBQW9CakIsVUFBVWtCLE1BQU0sQ0FBQyxDQUFDQyxLQUFLM0MsUUFBVTJDLE1BQU0zQyxNQUFNTyxZQUFZLEVBQUU7SUFDckYsTUFBTXFDLDBCQUEwQnBCLFVBQVVrQixNQUFNLENBQUMsQ0FBQ0MsS0FBSzNDLFFBQVUyQyxNQUFNM0MsTUFBTWEsaUJBQWlCLEVBQUU7SUFDaEcsTUFBTWdDLHdCQUF3QnJCLFVBQVVrQixNQUFNLENBQUMsQ0FBQ0MsS0FBSzNDLFFBQVUyQyxNQUFNM0MsTUFBTUssZ0JBQWdCLEVBQUU7SUFFN0YscUJBQ0UsOERBQUMrQjtRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDQzt3QkFBR0QsV0FBVTtrQ0FBc0M7Ozs7OztrQ0FDcEQsOERBQUNFO3dCQUFFRixXQUFVO2tDQUFnQjs7Ozs7Ozs7Ozs7OzBCQUkvQiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDNUQsMEhBQUlBOzswQ0FDSCw4REFBQ0csZ0lBQVVBO2dDQUFDeUQsV0FBVTswQ0FDcEIsNEVBQUN4RCwrSEFBU0E7b0NBQUN3RCxXQUFVOztzREFDbkIsOERBQUNwRCx5R0FBVUE7NENBQUNvRCxXQUFVOzs7Ozs7d0NBQWlCOzs7Ozs7Ozs7Ozs7MENBSTNDLDhEQUFDM0QsaUlBQVdBOztrREFDViw4REFBQzBEO3dDQUFJQyxXQUFVOzs0Q0FBcUI7NENBQUtJLGtCQUFrQkssY0FBYzs7Ozs7OztrREFDekUsOERBQUNQO3dDQUFFRixXQUFVO2tEQUF5Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUkxQyw4REFBQzVELDBIQUFJQTs7MENBQ0gsOERBQUNHLGdJQUFVQTtnQ0FBQ3lELFdBQVU7MENBQ3BCLDRFQUFDeEQsK0hBQVNBO29DQUFDd0QsV0FBVTs7c0RBQ25CLDhEQUFDbkQseUdBQUtBOzRDQUFDbUQsV0FBVTs7Ozs7O3dDQUFpQjs7Ozs7Ozs7Ozs7OzBDQUl0Qyw4REFBQzNELGlJQUFXQTs7a0RBQ1YsOERBQUMwRDt3Q0FBSUMsV0FBVTs7NENBQXFCOzRDQUFLTyx3QkFBd0JFLGNBQWM7Ozs7Ozs7a0RBQy9FLDhEQUFDUDt3Q0FBRUYsV0FBVTtrREFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FJekMsOERBQUM1RCwwSEFBSUE7OzBDQUNILDhEQUFDRyxnSUFBVUE7Z0NBQUN5RCxXQUFVOzBDQUNwQiw0RUFBQ3hELCtIQUFTQTtvQ0FBQ3dELFdBQVU7O3NEQUNuQiw4REFBQ3JELHlHQUFLQTs0Q0FBQ3FELFdBQVU7Ozs7Ozt3Q0FBaUI7Ozs7Ozs7Ozs7OzswQ0FJdEMsOERBQUMzRCxpSUFBV0E7O2tEQUNWLDhEQUFDMEQ7d0NBQUlDLFdBQVU7a0RBQXNCUTs7Ozs7O2tEQUNyQyw4REFBQ047d0NBQUVGLFdBQVU7a0RBQXlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBSzVDLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUM1RCwwSEFBSUE7OzBDQUNILDhEQUFDRyxnSUFBVUE7O2tEQUNULDhEQUFDQywrSEFBU0E7d0NBQUN3RCxXQUFVOzswREFDbkIsOERBQUN0RCx5R0FBTUE7Z0RBQUNzRCxXQUFVOzs7Ozs7MERBQ2xCLDhEQUFDVTswREFBSzs7Ozs7Ozs7Ozs7O2tEQUVSLDhEQUFDcEUscUlBQWVBO2tEQUFDOzs7Ozs7Ozs7Ozs7MENBRW5CLDhEQUFDRCxpSUFBV0E7MENBQ1YsNEVBQUMwRDtvQ0FBSUMsV0FBVTs4Q0FDWmIsVUFBVXpCLEdBQUcsQ0FBQyxDQUFDQyxzQkFDZCw4REFBQ29DOzRDQUFtQkMsV0FBVTs7OERBQzVCLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUN2RCwyRUFBS0E7NERBQUN1RCxXQUFXRixrQkFBa0JuQyxNQUFNZSxJQUFJOztnRUFBRztnRUFDN0NmLE1BQU1lLElBQUk7Ozs7Ozs7c0VBRWQsOERBQUNxQjs7OEVBQ0MsOERBQUNHO29FQUFFRixXQUFVOzhFQUF1QnJDLE1BQU1KLElBQUk7Ozs7Ozs4RUFDOUMsOERBQUMyQztvRUFBRUYsV0FBVTs7d0VBQ1ZyQyxNQUFNSyxnQkFBZ0I7d0VBQUM7d0VBQWNMLE1BQU1jLFlBQVk7d0VBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBSS9ELDhEQUFDc0I7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRTs0REFBRUYsV0FBVTs7Z0VBQXNCO2dFQUFLckMsTUFBTU8sWUFBWSxDQUFDdUMsY0FBYzs7Ozs7OztzRUFDekUsOERBQUNWOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ3ZELDJFQUFLQTtvRUFBQ2tFLFNBQVE7b0VBQVVYLFdBQVdILGNBQWNsQyxNQUFNZ0IsS0FBSzs4RUFDMURpQixhQUFhakMsTUFBTWdCLEtBQUs7Ozs7Ozs4RUFFM0IsOERBQUMrQjtvRUFBS1YsV0FBVTs7d0VBQXdCO3dFQUNqQ3JDLE1BQU1hLGlCQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MkNBbkIxQmIsTUFBTVMsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQThCMUIsOERBQUNoQywwSEFBSUE7OzBDQUNILDhEQUFDRyxnSUFBVUE7O2tEQUNULDhEQUFDQywrSEFBU0E7a0RBQUM7Ozs7OztrREFDWCw4REFBQ0YscUlBQWVBO2tEQUFDOzs7Ozs7Ozs7Ozs7MENBRW5CLDhEQUFDRCxpSUFBV0E7MENBQ1YsNEVBQUNhLHFKQUFtQkE7b0NBQUMwRCxPQUFNO29DQUFPQyxRQUFROzhDQUN4Qyw0RUFBQ3pELDJJQUFTQTt3Q0FBQzBELE1BQU16Qjs7MERBQ2YsOERBQUNyQyxnSkFBYUE7Z0RBQUMrRCxpQkFBZ0I7Ozs7OzswREFDL0IsOERBQUNqRSx3SUFBS0E7Z0RBQUNrRSxTQUFROzs7Ozs7MERBQ2YsOERBQUNqRSx3SUFBS0E7Ozs7OzBEQUNOLDhEQUFDRSwwSUFBT0E7Z0RBQ05nRSxXQUFXLENBQUNDLE9BQVkzRCxPQUFjO3dEQUNwQ0EsU0FBUyxlQUFlLE9BQThCLE9BQXZCMkQsTUFBTVQsY0FBYyxNQUNuRGxELFNBQVMscUJBQXFCLE9BQWEsT0FBTjJELFNBQVVBO3dEQUMvQzNELFNBQVMsZUFBZSxVQUN4QkEsU0FBUyxxQkFBcUIsZ0JBQWdCO3FEQUMvQzs7Ozs7OzBEQUVILDhEQUFDSix1SUFBSUE7Z0RBQ0hnRSxNQUFLO2dEQUNMSCxTQUFRO2dEQUNSSSxTQUFRO2dEQUNSQyxRQUFPO2dEQUNQQyxNQUFLO2dEQUNMQyxhQUFhOzs7Ozs7MERBRWYsOERBQUNwRSx1SUFBSUE7Z0RBQ0hnRSxNQUFLO2dEQUNMSCxTQUFRO2dEQUNSSSxTQUFRO2dEQUNSQyxRQUFPO2dEQUNQQyxNQUFLO2dEQUNMQyxhQUFhOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUy9CO0dBMU1nQnJDO0tBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9Db2FjaFBlcmZvcm1hbmNlU2VjdGlvbi50c3g/YmU2NSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmREZXNjcmlwdGlvbiwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlIH0gZnJvbSAnQG51dHJpcHJvL3VpJ1xuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tICdAbnV0cmlwcm8vdWknXG5pbXBvcnQgeyBUcm9waHksIFVzZXJzLCBEb2xsYXJTaWduLCBUcmVuZGluZ1VwLCBTdGFyLCBBd2FyZCB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcbmltcG9ydCB7IFxuICBCYXJDaGFydCwgXG4gIEJhciwgXG4gIFhBeGlzLCBcbiAgWUF4aXMsIFxuICBDYXJ0ZXNpYW5HcmlkLCBcbiAgVG9vbHRpcCwgXG4gIFJlc3BvbnNpdmVDb250YWluZXIsXG4gIExpbmVDaGFydCxcbiAgTGluZSxcbiAgQXJlYSxcbiAgQXJlYUNoYXJ0XG59IGZyb20gJ3JlY2hhcnRzJ1xuXG5pbnRlcmZhY2UgQ29hY2hQZXJmb3JtYW5jZSB7XG4gIGlkOiBzdHJpbmdcbiAgbmFtZTogc3RyaW5nXG4gIHRvdGFsUmVmZXJyYWxzOiBudW1iZXJcbiAgbW9udGhseVJlZmVycmFsczogbnVtYmVyXG4gIHRvdGFsU2FsZXM6IG51bWJlclxuICBtb250aGx5U2FsZXM6IG51bWJlclxuICBjb21taXNzaW9uRWFybmVkOiBudW1iZXJcbiAgbW9udGhseUNvbW1pc3Npb246IG51bWJlclxuICByZWZlcnJhbFJhdGU6IG51bWJlclxuICByYW5rOiBudW1iZXJcbiAgdHJlbmQ6ICd1cCcgfCAnZG93bicgfCAnc3RhYmxlJ1xufVxuXG5pbnRlcmZhY2UgTW9udGhseVBlcmZvcm1hbmNlRGF0YSB7XG4gIG1vbnRoOiBzdHJpbmdcbiAgdG90YWxTYWxlczogbnVtYmVyXG4gIHRvdGFsQ29tbWlzc2lvbnM6IG51bWJlclxuICBuZXdSZWZlcnJhbHM6IG51bWJlclxufVxuXG4vLyBNb2NrIGRhdGEgZ2VuZXJhdG9yc1xuY29uc3QgZ2VuZXJhdGVDb2FjaFBlcmZvcm1hbmNlRGF0YSA9ICgpOiBDb2FjaFBlcmZvcm1hbmNlW10gPT4ge1xuICBjb25zdCBjb2FjaGVzID0gW1xuICAgIHsgbmFtZTogJ01hcmlhIFJvZHJpZ3VleicsIGJhc2VSZWZlcnJhbHM6IDQ1LCBiYXNlU2FsZXM6IDEyNTAwIH0sXG4gICAgeyBuYW1lOiAnSmFtZXMgV2lsc29uJywgYmFzZVJlZmVycmFsczogMzgsIGJhc2VTYWxlczogMTA4MDAgfSxcbiAgICB7IG5hbWU6ICdTYXJhaCBDaGVuJywgYmFzZVJlZmVycmFsczogMzIsIGJhc2VTYWxlczogOTIwMCB9LFxuICAgIHsgbmFtZTogJ01pY2hhZWwgQnJvd24nLCBiYXNlUmVmZXJyYWxzOiAyOCwgYmFzZVNhbGVzOiA4MTAwIH1cbiAgXVxuXG4gIHJldHVybiBjb2FjaGVzLm1hcCgoY29hY2gsIGluZGV4KSA9PiB7XG4gICAgY29uc3QgbW9udGhseVZhcmlhdGlvbiA9IDAuOCArIE1hdGgucmFuZG9tKCkgKiAwLjQgLy8gODAtMTIwJVxuICAgIGNvbnN0IG1vbnRobHlSZWZlcnJhbHMgPSBNYXRoLnJvdW5kKGNvYWNoLmJhc2VSZWZlcnJhbHMgKiAwLjE1ICogbW9udGhseVZhcmlhdGlvbilcbiAgICBjb25zdCBtb250aGx5U2FsZXMgPSBNYXRoLnJvdW5kKGNvYWNoLmJhc2VTYWxlcyAqIDAuMTUgKiBtb250aGx5VmFyaWF0aW9uKVxuICAgIGNvbnN0IGNvbW1pc3Npb25SYXRlID0gMC4wNSArIChpbmRleCAqIDAuMDEpIC8vIDUtOCUgY29tbWlzc2lvbiByYXRlXG4gICAgXG4gICAgcmV0dXJuIHtcbiAgICAgIGlkOiBgY29hY2gtJHtpbmRleCArIDF9YCxcbiAgICAgIG5hbWU6IGNvYWNoLm5hbWUsXG4gICAgICB0b3RhbFJlZmVycmFsczogY29hY2guYmFzZVJlZmVycmFscyxcbiAgICAgIG1vbnRobHlSZWZlcnJhbHMsXG4gICAgICB0b3RhbFNhbGVzOiBjb2FjaC5iYXNlU2FsZXMsXG4gICAgICBtb250aGx5U2FsZXMsXG4gICAgICBjb21taXNzaW9uRWFybmVkOiBNYXRoLnJvdW5kKGNvYWNoLmJhc2VTYWxlcyAqIGNvbW1pc3Npb25SYXRlKSxcbiAgICAgIG1vbnRobHlDb21taXNzaW9uOiBNYXRoLnJvdW5kKG1vbnRobHlTYWxlcyAqIGNvbW1pc3Npb25SYXRlKSxcbiAgICAgIHJlZmVycmFsUmF0ZTogY29tbWlzc2lvblJhdGUgKiAxMDAsXG4gICAgICByYW5rOiBpbmRleCArIDEsXG4gICAgICB0cmVuZDogTWF0aC5yYW5kb20oKSA+IDAuMyA/ICd1cCcgOiBNYXRoLnJhbmRvbSgpID4gMC41ID8gJ3N0YWJsZScgOiAnZG93bidcbiAgICB9XG4gIH0pXG59XG5cbmNvbnN0IGdlbmVyYXRlTW9udGhseVBlcmZvcm1hbmNlRGF0YSA9ICgpOiBNb250aGx5UGVyZm9ybWFuY2VEYXRhW10gPT4ge1xuICBjb25zdCBtb250aHMgPSBbJ0phbicsICdGZWInLCAnTWFyJywgJ0FwcicsICdNYXknLCAnSnVuJ11cbiAgcmV0dXJuIG1vbnRocy5tYXAobW9udGggPT4gKHtcbiAgICBtb250aCxcbiAgICB0b3RhbFNhbGVzOiBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAxNTAwMCkgKyAyNTAwMCxcbiAgICB0b3RhbENvbW1pc3Npb25zOiBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiA4MDApICsgMTIwMCxcbiAgICBuZXdSZWZlcnJhbHM6IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDIwKSArIDE1XG4gIH0pKVxufVxuXG5leHBvcnQgZnVuY3Rpb24gQ29hY2hQZXJmb3JtYW5jZVNlY3Rpb24oKSB7XG4gIGNvbnN0IFtjb2FjaERhdGEsIHNldENvYWNoRGF0YV0gPSB1c2VTdGF0ZTxDb2FjaFBlcmZvcm1hbmNlW10+KFtdKVxuICBjb25zdCBbbW9udGhseURhdGEsIHNldE1vbnRobHlEYXRhXSA9IHVzZVN0YXRlPE1vbnRobHlQZXJmb3JtYW5jZURhdGFbXT4oW10pXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBTaW11bGF0ZSBBUEkgY2FsbCBkZWxheVxuICAgIGNvbnN0IHRpbWVyID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICBzZXRDb2FjaERhdGEoZ2VuZXJhdGVDb2FjaFBlcmZvcm1hbmNlRGF0YSgpKVxuICAgICAgc2V0TW9udGhseURhdGEoZ2VuZXJhdGVNb250aGx5UGVyZm9ybWFuY2VEYXRhKCkpXG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKVxuICAgIH0sIDE1MDApXG5cbiAgICByZXR1cm4gKCkgPT4gY2xlYXJUaW1lb3V0KHRpbWVyKVxuICB9LCBbXSlcblxuICBjb25zdCBnZXRUcmVuZEljb24gPSAodHJlbmQ6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAodHJlbmQpIHtcbiAgICAgIGNhc2UgJ3VwJzogcmV0dXJuICfihpfvuI8nXG4gICAgICBjYXNlICdkb3duJzogcmV0dXJuICfihpjvuI8nXG4gICAgICBkZWZhdWx0OiByZXR1cm4gJ+Keoe+4jydcbiAgICB9XG4gIH1cblxuICBjb25zdCBnZXRUcmVuZENvbG9yID0gKHRyZW5kOiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHRyZW5kKSB7XG4gICAgICBjYXNlICd1cCc6IHJldHVybiAndGV4dC1ncmVlbi02MDAgYmctZ3JlZW4tNTAgYm9yZGVyLWdyZWVuLTIwMCdcbiAgICAgIGNhc2UgJ2Rvd24nOiByZXR1cm4gJ3RleHQtcmVkLTYwMCBiZy1yZWQtNTAgYm9yZGVyLXJlZC0yMDAnXG4gICAgICBkZWZhdWx0OiByZXR1cm4gJ3RleHQtZ3JheS02MDAgYmctZ3JheS01MCBib3JkZXItZ3JheS0yMDAnXG4gICAgfVxuICB9XG5cbiAgY29uc3QgZ2V0UmFua0JhZGdlQ29sb3IgPSAocmFuazogbnVtYmVyKSA9PiB7XG4gICAgc3dpdGNoIChyYW5rKSB7XG4gICAgICBjYXNlIDE6IHJldHVybiAnYmcteWVsbG93LTEwMCB0ZXh0LXllbGxvdy04MDAgYm9yZGVyLXllbGxvdy0zMDAnXG4gICAgICBjYXNlIDI6IHJldHVybiAnYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMCBib3JkZXItZ3JheS0zMDAnXG4gICAgICBjYXNlIDM6IHJldHVybiAnYmctb3JhbmdlLTEwMCB0ZXh0LW9yYW5nZS04MDAgYm9yZGVyLW9yYW5nZS0zMDAnXG4gICAgICBkZWZhdWx0OiByZXR1cm4gJ2JnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDAgYm9yZGVyLWJsdWUtMzAwJ1xuICAgIH1cbiAgfVxuXG4gIGlmIChsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiBtZDptYi04XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNFwiPlxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPkNvYWNoIFBlcmZvcm1hbmNlPC9oMj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+UmVmZXJyYWwgdHJhY2tpbmcsIGNvbW1pc3Npb24gYW5hbHl0aWNzLCBhbmQgcGVyZm9ybWFuY2UgbWV0cmljczwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMiBnYXAtNCBtZDpnYXAtNlwiPlxuICAgICAgICAgIHtbMSwgMiwgM10ubWFwKChpKSA9PiAoXG4gICAgICAgICAgICA8Q2FyZCBrZXk9e2l9PlxuICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNCBiZy1ncmF5LTIwMCByb3VuZGVkIGFuaW1hdGUtcHVsc2VcIj48L2Rpdj5cbiAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTY0IGJnLWdyYXktMTAwIHJvdW5kZWQgYW5pbWF0ZS1wdWxzZVwiPjwvZGl2PlxuICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgICkpfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIGNvbnN0IHRvdGFsTW9udGhseVNhbGVzID0gY29hY2hEYXRhLnJlZHVjZSgoc3VtLCBjb2FjaCkgPT4gc3VtICsgY29hY2gubW9udGhseVNhbGVzLCAwKVxuICBjb25zdCB0b3RhbE1vbnRobHlDb21taXNzaW9ucyA9IGNvYWNoRGF0YS5yZWR1Y2UoKHN1bSwgY29hY2gpID0+IHN1bSArIGNvYWNoLm1vbnRobHlDb21taXNzaW9uLCAwKVxuICBjb25zdCB0b3RhbE1vbnRobHlSZWZlcnJhbHMgPSBjb2FjaERhdGEucmVkdWNlKChzdW0sIGNvYWNoKSA9PiBzdW0gKyBjb2FjaC5tb250aGx5UmVmZXJyYWxzLCAwKVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtYi02IG1kOm1iLThcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNFwiPlxuICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5Db2FjaCBQZXJmb3JtYW5jZTwvaDI+XG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5SZWZlcnJhbCB0cmFja2luZywgY29tbWlzc2lvbiBhbmFseXRpY3MsIGFuZCBwZXJmb3JtYW5jZSBtZXRyaWNzPC9wPlxuICAgICAgPC9kaXY+XG4gICAgICBcbiAgICAgIHsvKiBTdW1tYXJ5IENhcmRzICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIHNtOmdyaWQtY29scy0zIGdhcC00IG1kOmdhcC02IG1iLTZcIj5cbiAgICAgICAgPENhcmQ+XG4gICAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwicGItMlwiPlxuICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS02MDAgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPERvbGxhclNpZ24gY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgTW9udGhseSBDb2FjaCBTYWxlc1xuICAgICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkXCI+QVdHIHt0b3RhbE1vbnRobHlTYWxlcy50b0xvY2FsZVN0cmluZygpfTwvZGl2PlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyZWVuLTYwMFwiPisxMiUgZnJvbSBsYXN0IG1vbnRoPC9wPlxuICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgIDwvQ2FyZD5cblxuICAgICAgICA8Q2FyZD5cbiAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJwYi0yXCI+XG4gICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTYwMCBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8QXdhcmQgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgVG90YWwgQ29tbWlzc2lvbnNcbiAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZFwiPkFXRyB7dG90YWxNb250aGx5Q29tbWlzc2lvbnMudG9Mb2NhbGVTdHJpbmcoKX08L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ibHVlLTYwMFwiPlRoaXMgbW9udGg8L3A+XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgIDxDYXJkPlxuICAgICAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cInBiLTJcIj5cbiAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNjAwIGZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxVc2VycyBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICBOZXcgUmVmZXJyYWxzXG4gICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGRcIj57dG90YWxNb250aGx5UmVmZXJyYWxzfTwvZGl2PlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyZWVuLTYwMFwiPlRoaXMgbW9udGg8L3A+XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMiBnYXAtNCBtZDpnYXAtNlwiPlxuICAgICAgICB7LyogQ29hY2ggTGVhZGVyYm9hcmQgKi99XG4gICAgICAgIDxDYXJkPlxuICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgPFRyb3BoeSBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQteWVsbG93LTUwMFwiIC8+XG4gICAgICAgICAgICAgIDxzcGFuPkNvYWNoIExlYWRlcmJvYXJkPC9zcGFuPlxuICAgICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uPlRvcCBwZXJmb3JtaW5nIGNvYWNoZXMgdGhpcyBtb250aDwvQ2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICB7Y29hY2hEYXRhLm1hcCgoY29hY2gpID0+IChcbiAgICAgICAgICAgICAgICA8ZGl2IGtleT17Y29hY2guaWR9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwLTMgYm9yZGVyIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICAgIDxCYWRnZSBjbGFzc05hbWU9e2dldFJhbmtCYWRnZUNvbG9yKGNvYWNoLnJhbmspfT5cbiAgICAgICAgICAgICAgICAgICAgICAje2NvYWNoLnJhbmt9XG4gICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1zbVwiPntjb2FjaC5uYW1lfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtjb2FjaC5tb250aGx5UmVmZXJyYWxzfSByZWZlcnJhbHMg4oCiIHtjb2FjaC5yZWZlcnJhbFJhdGV9JSByYXRlXG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtc21cIj5BV0cge2NvYWNoLm1vbnRobHlTYWxlcy50b0xvY2FsZVN0cmluZygpfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgdmFyaWFudD1cIm91dGxpbmVcIiBjbGFzc05hbWU9e2dldFRyZW5kQ29sb3IoY29hY2gudHJlbmQpfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtnZXRUcmVuZEljb24oY29hY2gudHJlbmQpfVxuICAgICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBBV0cge2NvYWNoLm1vbnRobHlDb21taXNzaW9ufVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgey8qIE1vbnRobHkgUGVyZm9ybWFuY2UgVHJlbmRzICovfVxuICAgICAgICA8Q2FyZD5cbiAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkVGl0bGU+TW9udGhseSBQZXJmb3JtYW5jZSBUcmVuZHM8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24+Q29hY2ggcHJvZ3JhbSBzYWxlcyBhbmQgY29tbWlzc2lvbiB0cmVuZHM8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgPFJlc3BvbnNpdmVDb250YWluZXIgd2lkdGg9XCIxMDAlXCIgaGVpZ2h0PXsyNTB9PlxuICAgICAgICAgICAgICA8QXJlYUNoYXJ0IGRhdGE9e21vbnRobHlEYXRhfT5cbiAgICAgICAgICAgICAgICA8Q2FydGVzaWFuR3JpZCBzdHJva2VEYXNoYXJyYXk9XCIzIDNcIiAvPlxuICAgICAgICAgICAgICAgIDxYQXhpcyBkYXRhS2V5PVwibW9udGhcIiAvPlxuICAgICAgICAgICAgICAgIDxZQXhpcyAvPlxuICAgICAgICAgICAgICAgIDxUb29sdGlwIFxuICAgICAgICAgICAgICAgICAgZm9ybWF0dGVyPXsodmFsdWU6IGFueSwgbmFtZTogYW55KSA9PiBbXG4gICAgICAgICAgICAgICAgICAgIG5hbWUgPT09ICd0b3RhbFNhbGVzJyA/IGBBV0cgJHt2YWx1ZS50b0xvY2FsZVN0cmluZygpfWAgOiBcbiAgICAgICAgICAgICAgICAgICAgbmFtZSA9PT0gJ3RvdGFsQ29tbWlzc2lvbnMnID8gYEFXRyAke3ZhbHVlfWAgOiB2YWx1ZSxcbiAgICAgICAgICAgICAgICAgICAgbmFtZSA9PT0gJ3RvdGFsU2FsZXMnID8gJ1NhbGVzJyA6XG4gICAgICAgICAgICAgICAgICAgIG5hbWUgPT09ICd0b3RhbENvbW1pc3Npb25zJyA/ICdDb21taXNzaW9ucycgOiAnUmVmZXJyYWxzJ1xuICAgICAgICAgICAgICAgICAgXX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDxBcmVhIFxuICAgICAgICAgICAgICAgICAgdHlwZT1cIm1vbm90b25lXCIgXG4gICAgICAgICAgICAgICAgICBkYXRhS2V5PVwidG90YWxTYWxlc1wiIFxuICAgICAgICAgICAgICAgICAgc3RhY2tJZD1cIjFcIlxuICAgICAgICAgICAgICAgICAgc3Ryb2tlPVwiIzNCODJGNlwiIFxuICAgICAgICAgICAgICAgICAgZmlsbD1cIiMzQjgyRjZcIiBcbiAgICAgICAgICAgICAgICAgIGZpbGxPcGFjaXR5PXswLjZ9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8QXJlYSBcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJtb25vdG9uZVwiIFxuICAgICAgICAgICAgICAgICAgZGF0YUtleT1cInRvdGFsQ29tbWlzc2lvbnNcIiBcbiAgICAgICAgICAgICAgICAgIHN0YWNrSWQ9XCIyXCJcbiAgICAgICAgICAgICAgICAgIHN0cm9rZT1cIiMxMEI5ODFcIiBcbiAgICAgICAgICAgICAgICAgIGZpbGw9XCIjMTBCOTgxXCIgXG4gICAgICAgICAgICAgICAgICBmaWxsT3BhY2l0eT17MC42fVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvQXJlYUNoYXJ0PlxuICAgICAgICAgICAgPC9SZXNwb25zaXZlQ29udGFpbmVyPlxuICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgIDwvQ2FyZD5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkRGVzY3JpcHRpb24iLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiQmFkZ2UiLCJUcm9waHkiLCJVc2VycyIsIkRvbGxhclNpZ24iLCJBd2FyZCIsIlhBeGlzIiwiWUF4aXMiLCJDYXJ0ZXNpYW5HcmlkIiwiVG9vbHRpcCIsIlJlc3BvbnNpdmVDb250YWluZXIiLCJBcmVhIiwiQXJlYUNoYXJ0IiwiZ2VuZXJhdGVDb2FjaFBlcmZvcm1hbmNlRGF0YSIsImNvYWNoZXMiLCJuYW1lIiwiYmFzZVJlZmVycmFscyIsImJhc2VTYWxlcyIsIm1hcCIsImNvYWNoIiwiaW5kZXgiLCJtb250aGx5VmFyaWF0aW9uIiwiTWF0aCIsInJhbmRvbSIsIm1vbnRobHlSZWZlcnJhbHMiLCJyb3VuZCIsIm1vbnRobHlTYWxlcyIsImNvbW1pc3Npb25SYXRlIiwiaWQiLCJ0b3RhbFJlZmVycmFscyIsInRvdGFsU2FsZXMiLCJjb21taXNzaW9uRWFybmVkIiwibW9udGhseUNvbW1pc3Npb24iLCJyZWZlcnJhbFJhdGUiLCJyYW5rIiwidHJlbmQiLCJnZW5lcmF0ZU1vbnRobHlQZXJmb3JtYW5jZURhdGEiLCJtb250aHMiLCJtb250aCIsImZsb29yIiwidG90YWxDb21taXNzaW9ucyIsIm5ld1JlZmVycmFscyIsIkNvYWNoUGVyZm9ybWFuY2VTZWN0aW9uIiwiY29hY2hEYXRhIiwic2V0Q29hY2hEYXRhIiwibW9udGhseURhdGEiLCJzZXRNb250aGx5RGF0YSIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwidGltZXIiLCJzZXRUaW1lb3V0IiwiY2xlYXJUaW1lb3V0IiwiZ2V0VHJlbmRJY29uIiwiZ2V0VHJlbmRDb2xvciIsImdldFJhbmtCYWRnZUNvbG9yIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDIiLCJwIiwiaSIsInRvdGFsTW9udGhseVNhbGVzIiwicmVkdWNlIiwic3VtIiwidG90YWxNb250aGx5Q29tbWlzc2lvbnMiLCJ0b3RhbE1vbnRobHlSZWZlcnJhbHMiLCJ0b0xvY2FsZVN0cmluZyIsInNwYW4iLCJ2YXJpYW50Iiwid2lkdGgiLCJoZWlnaHQiLCJkYXRhIiwic3Ryb2tlRGFzaGFycmF5IiwiZGF0YUtleSIsImZvcm1hdHRlciIsInZhbHVlIiwidHlwZSIsInN0YWNrSWQiLCJzdHJva2UiLCJmaWxsIiwiZmlsbE9wYWNpdHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/CoachPerformanceSection.tsx\n"));

/***/ })

});