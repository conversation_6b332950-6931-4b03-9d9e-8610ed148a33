"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/../../packages/database/src/index.ts":
/*!********************************************!*\
  !*** ../../packages/database/src/index.ts ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomerQueries: function() { return /* reexport safe */ _queries_customers__WEBPACK_IMPORTED_MODULE_2__.CustomerQueries; },\n/* harmony export */   InventoryQueries: function() { return /* reexport safe */ _queries_inventory__WEBPACK_IMPORTED_MODULE_5__.InventoryQueries; },\n/* harmony export */   ProductQueries: function() { return /* reexport safe */ _queries_products__WEBPACK_IMPORTED_MODULE_1__.ProductQueries; },\n/* harmony export */   PurchaseOrderQueries: function() { return /* reexport safe */ _queries_purchase_orders__WEBPACK_IMPORTED_MODULE_4__.PurchaseOrderQueries; },\n/* harmony export */   TransactionQueries: function() { return /* reexport safe */ _queries_transactions__WEBPACK_IMPORTED_MODULE_3__.TransactionQueries; },\n/* harmony export */   buildPaginationQuery: function() { return /* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_7__.buildPaginationQuery; },\n/* harmony export */   buildSearchQuery: function() { return /* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_7__.buildSearchQuery; },\n/* harmony export */   buildSortQuery: function() { return /* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_7__.buildSortQuery; },\n/* harmony export */   createClient: function() { return /* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_0__.createClient; },\n/* harmony export */   formatCurrency: function() { return /* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency; },\n/* harmony export */   formatDateForDB: function() { return /* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_7__.formatDateForDB; },\n/* harmony export */   handleSupabaseError: function() { return /* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_7__.handleSupabaseError; },\n/* harmony export */   isValidUUID: function() { return /* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_7__.isValidUUID; },\n/* harmony export */   parseDBDate: function() { return /* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_7__.parseDBDate; },\n/* harmony export */   supabase: function() { return /* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_0__.supabase; },\n/* harmony export */   withTransaction: function() { return /* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_7__.withTransaction; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/../../packages/database/src/client.ts\");\n/* harmony import */ var _queries_products__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./queries/products */ \"(app-pages-browser)/../../packages/database/src/queries/products.ts\");\n/* harmony import */ var _queries_customers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./queries/customers */ \"(app-pages-browser)/../../packages/database/src/queries/customers.ts\");\n/* harmony import */ var _queries_transactions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./queries/transactions */ \"(app-pages-browser)/../../packages/database/src/queries/transactions.ts\");\n/* harmony import */ var _queries_purchase_orders__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./queries/purchase-orders */ \"(app-pages-browser)/../../packages/database/src/queries/purchase-orders.ts\");\n/* harmony import */ var _queries_inventory__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./queries/inventory */ \"(app-pages-browser)/../../packages/database/src/queries/inventory.ts\");\n/* harmony import */ var _types_supabase__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./types/supabase */ \"(app-pages-browser)/../../packages/database/src/types/supabase.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./utils */ \"(app-pages-browser)/../../packages/database/src/utils.ts\");\n// Export database client\n\n// Export query functions\n\n\n\n\n\n// Export types\n\n// Export utilities\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy9kYXRhYmFzZS9zcmMvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEseUJBQXlCO0FBQ3lCO0FBRWxELHlCQUF5QjtBQUMyQjtBQUNFO0FBQ007QUFDSztBQUNWO0FBT3ZELGVBQWU7QUFDa0I7QUFFakMsbUJBQW1CO0FBQ0siLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2RhdGFiYXNlL3NyYy9pbmRleC50cz9mODg0Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydCBkYXRhYmFzZSBjbGllbnRcbmV4cG9ydCB7IHN1cGFiYXNlLCBjcmVhdGVDbGllbnQgfSBmcm9tIFwiLi9jbGllbnRcIjtcblxuLy8gRXhwb3J0IHF1ZXJ5IGZ1bmN0aW9uc1xuZXhwb3J0IHsgUHJvZHVjdFF1ZXJpZXMgfSBmcm9tIFwiLi9xdWVyaWVzL3Byb2R1Y3RzXCI7XG5leHBvcnQgeyBDdXN0b21lclF1ZXJpZXMgfSBmcm9tIFwiLi9xdWVyaWVzL2N1c3RvbWVyc1wiO1xuZXhwb3J0IHsgVHJhbnNhY3Rpb25RdWVyaWVzIH0gZnJvbSBcIi4vcXVlcmllcy90cmFuc2FjdGlvbnNcIjtcbmV4cG9ydCB7IFB1cmNoYXNlT3JkZXJRdWVyaWVzIH0gZnJvbSBcIi4vcXVlcmllcy9wdXJjaGFzZS1vcmRlcnNcIjtcbmV4cG9ydCB7IEludmVudG9yeVF1ZXJpZXMgfSBmcm9tIFwiLi9xdWVyaWVzL2ludmVudG9yeVwiO1xuXG4vLyBFeHBvcnQgdHlwZXNcbmV4cG9ydCB0eXBlIHsgVHJhbnNhY3Rpb25XaXRoSXRlbXMsIENyZWF0ZVRyYW5zYWN0aW9uRGF0YSB9IGZyb20gXCIuL3F1ZXJpZXMvdHJhbnNhY3Rpb25zXCI7XG5leHBvcnQgdHlwZSB7IFB1cmNoYXNlT3JkZXJXaXRoSXRlbXMsIENyZWF0ZVB1cmNoYXNlT3JkZXJEYXRhIH0gZnJvbSBcIi4vcXVlcmllcy9wdXJjaGFzZS1vcmRlcnNcIjtcbmV4cG9ydCB0eXBlIHsgUHJvZHVjdEJhdGNoV2l0aFByb2R1Y3QsIEludmVudG9yeUFsZXJ0LCBGSUZPQmF0Y2ggfSBmcm9tIFwiLi9xdWVyaWVzL2ludmVudG9yeVwiO1xuXG4vLyBFeHBvcnQgdHlwZXNcbmV4cG9ydCAqIGZyb20gXCIuL3R5cGVzL3N1cGFiYXNlXCI7XG5cbi8vIEV4cG9ydCB1dGlsaXRpZXNcbmV4cG9ydCAqIGZyb20gXCIuL3V0aWxzXCI7XG4iXSwibmFtZXMiOlsic3VwYWJhc2UiLCJjcmVhdGVDbGllbnQiLCJQcm9kdWN0UXVlcmllcyIsIkN1c3RvbWVyUXVlcmllcyIsIlRyYW5zYWN0aW9uUXVlcmllcyIsIlB1cmNoYXNlT3JkZXJRdWVyaWVzIiwiSW52ZW50b3J5UXVlcmllcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/database/src/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/database/src/queries/inventory.ts":
/*!********************************************************!*\
  !*** ../../packages/database/src/queries/inventory.ts ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InventoryQueries: function() { return /* binding */ InventoryQueries; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client */ \"(app-pages-browser)/../../packages/database/src/client.ts\");\n\nclass InventoryQueries {\n    // Product Batch Management\n    async getAllBatches() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 100, offset = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        const { data, error } = await this.supabase.from(\"product_batches\").select(\"\\n        *,\\n        products (\\n          id,\\n          name,\\n          sku,\\n          stock_quantity\\n        )\\n      \").order(\"expiry_date\", {\n            ascending: true\n        }).range(offset, offset + limit - 1);\n        if (error) throw error;\n        return data;\n    }\n    async getBatchesByProduct(productId) {\n        const { data, error } = await this.supabase.from(\"product_batches\").select(\"*\").eq(\"product_id\", productId).eq(\"status\", \"active\").order(\"fifo_priority\", {\n            ascending: true\n        });\n        if (error) throw error;\n        return data;\n    }\n    async getExpiringBatches() {\n        let daysAhead = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 30;\n        const futureDate = new Date();\n        futureDate.setDate(futureDate.getDate() + daysAhead);\n        const { data, error } = await this.supabase.from(\"product_batches\").select(\"\\n        *,\\n        products (\\n          id,\\n          name,\\n          sku\\n        )\\n      \").lte(\"expiry_date\", futureDate.toISOString().split(\"T\")[0]).eq(\"status\", \"active\").gt(\"quantity_available\", 0).order(\"expiry_date\", {\n            ascending: true\n        });\n        if (error) throw error;\n        return data;\n    }\n    async createBatch(batchData) {\n        // Get the next FIFO priority for this product\n        const { data: lastBatch, error: priorityError } = await this.supabase.from(\"product_batches\").select(\"fifo_priority\").eq(\"product_id\", batchData.product_id).order(\"fifo_priority\", {\n            ascending: false\n        }).limit(1).single();\n        const nextPriority = lastBatch ? lastBatch.fifo_priority + 1 : 1;\n        const { data, error } = await this.supabase.from(\"product_batches\").insert({\n            ...batchData,\n            fifo_priority: nextPriority\n        }).select().single();\n        if (error) throw error;\n        return data;\n    }\n    async updateBatch(id, updates) {\n        const { data, error } = await this.supabase.from(\"product_batches\").update(updates).eq(\"id\", id).select().single();\n        if (error) throw error;\n        return data;\n    }\n    // FIFO Batch Selection\n    async getFIFOBatches(productId, quantityNeeded) {\n        const { data, error } = await this.supabase.from(\"product_batches\").select(\"id, batch_number, quantity_available, expiry_date, unit_cost\").eq(\"product_id\", productId).eq(\"status\", \"active\").gt(\"quantity_available\", 0).gt(\"expiry_date\", new Date().toISOString().split(\"T\")[0]).order(\"fifo_priority\", {\n            ascending: true\n        }).order(\"expiry_date\", {\n            ascending: true\n        });\n        if (error) throw error;\n        const batches = [];\n        let remainingQuantity = quantityNeeded;\n        for (const batch of data){\n            if (remainingQuantity <= 0) break;\n            const quantityFromBatch = Math.min(batch.quantity_available, remainingQuantity);\n            batches.push({\n                batch_id: batch.id,\n                batch_number: batch.batch_number,\n                available_quantity: quantityFromBatch,\n                expiry_date: batch.expiry_date,\n                unit_cost: batch.unit_cost\n            });\n            remainingQuantity -= quantityFromBatch;\n        }\n        return batches;\n    }\n    // Inventory Alerts\n    async getInventoryAlerts() {\n        const alerts = [];\n        // Get low stock alerts\n        const { data: lowStockProducts, error: lowStockError } = await this.supabase.from(\"products\").select(\"id, name, sku, stock_quantity, min_stock_level\").lte(\"stock_quantity\", this.supabase.rpc(\"min_stock_level\"));\n        if (!lowStockError && lowStockProducts) {\n            for (const product of lowStockProducts){\n                alerts.push({\n                    id: \"low-stock-\".concat(product.id),\n                    product_id: product.id,\n                    product_name: product.name,\n                    sku: product.sku,\n                    alert_type: product.stock_quantity === 0 ? \"out_of_stock\" : \"low_stock\",\n                    current_stock: product.stock_quantity,\n                    min_stock: product.min_stock_level,\n                    severity: product.stock_quantity === 0 ? \"high\" : product.stock_quantity <= product.min_stock_level / 2 ? \"medium\" : \"low\"\n                });\n            }\n        }\n        // Get expiry warnings\n        const expiringBatches = await this.getExpiringBatches(30);\n        for (const batch of expiringBatches){\n            var _batch_products, _batch_products1;\n            const daysUntilExpiry = Math.ceil((new Date(batch.expiry_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));\n            alerts.push({\n                id: \"expiry-\".concat(batch.id),\n                product_id: batch.product_id,\n                product_name: ((_batch_products = batch.products) === null || _batch_products === void 0 ? void 0 : _batch_products.name) || \"Unknown Product\",\n                sku: ((_batch_products1 = batch.products) === null || _batch_products1 === void 0 ? void 0 : _batch_products1.sku) || \"Unknown SKU\",\n                alert_type: \"expiry_warning\",\n                current_stock: batch.quantity_available,\n                min_stock: 0,\n                days_until_expiry: daysUntilExpiry,\n                batch_number: batch.batch_number,\n                severity: daysUntilExpiry <= 7 ? \"high\" : daysUntilExpiry <= 14 ? \"medium\" : \"low\"\n            });\n        }\n        return alerts;\n    }\n    // Stock Movements\n    async recordStockMovement(data) {\n        // Get current stock\n        const { data: product, error: productError } = await this.supabase.from(\"products\").select(\"stock_quantity\").eq(\"id\", data.product_id).single();\n        if (productError) throw productError;\n        const stockBefore = product.stock_quantity;\n        const stockAfter = stockBefore + data.quantity;\n        // Record the movement\n        const { data: movement, error: movementError } = await this.supabase.from(\"stock_movements\").insert({\n            product_id: data.product_id,\n            movement_type: data.movement_type,\n            quantity: data.quantity,\n            reference_id: data.reference_id,\n            reference_type: data.reference_type,\n            stock_before: stockBefore,\n            stock_after: stockAfter\n        }).select().single();\n        if (movementError) throw movementError;\n        // Update product stock\n        await this.supabase.from(\"products\").update({\n            stock_quantity: Math.max(0, stockAfter)\n        }).eq(\"id\", data.product_id);\n        return movement;\n    }\n    // Inventory Adjustments\n    async createAdjustment(data) {\n        const { data: adjustment, error } = await this.supabase.from(\"inventory_adjustments\").insert({\n            product_id: data.product_id,\n            adjustment_type: data.adjustment_type,\n            quantity_change: data.quantity_change,\n            reason: data.reason,\n            notes: data.notes,\n            unit_cost: data.unit_cost,\n            total_cost: data.unit_cost ? data.unit_cost * Math.abs(data.quantity_change) : null\n        }).select().single();\n        if (error) throw error;\n        // Record stock movement\n        await this.recordStockMovement({\n            product_id: data.product_id,\n            movement_type: \"adjustment\",\n            quantity: data.quantity_change,\n            reference_id: adjustment.id,\n            reference_type: \"adjustment\"\n        });\n        return adjustment;\n    }\n    async getAdjustmentHistory(productId) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 50;\n        let query = this.supabase.from(\"inventory_adjustments\").select(\"\\n        *,\\n        products (\\n          id,\\n          name,\\n          sku\\n        )\\n      \").order(\"created_at\", {\n            ascending: false\n        }).limit(limit);\n        if (productId) {\n            query = query.eq(\"product_id\", productId);\n        }\n        const { data, error } = await query;\n        if (error) throw error;\n        return data;\n    }\n    // Inventory Statistics\n    async getInventoryStats() {\n        // Total products\n        const { data: totalProducts, error: totalError } = await this.supabase.from(\"products\").select(\"id\", {\n            count: \"exact\"\n        });\n        // Low stock products\n        const { data: lowStockProducts, error: lowStockError } = await this.supabase.from(\"products\").select(\"id\", {\n            count: \"exact\"\n        }).lte(\"stock_quantity\", this.supabase.rpc(\"min_stock_level\"));\n        // Out of stock products\n        const { data: outOfStockProducts, error: outOfStockError } = await this.supabase.from(\"products\").select(\"id\", {\n            count: \"exact\"\n        }).eq(\"stock_quantity\", 0);\n        // Expiring batches (next 30 days)\n        const expiringBatches = await this.getExpiringBatches(30);\n        if (totalError || lowStockError || outOfStockError) {\n            throw totalError || lowStockError || outOfStockError;\n        }\n        return {\n            totalProducts: (totalProducts === null || totalProducts === void 0 ? void 0 : totalProducts.length) || 0,\n            lowStockProducts: (lowStockProducts === null || lowStockProducts === void 0 ? void 0 : lowStockProducts.length) || 0,\n            outOfStockProducts: (outOfStockProducts === null || outOfStockProducts === void 0 ? void 0 : outOfStockProducts.length) || 0,\n            expiringBatches: expiringBatches.length\n        };\n    }\n    constructor(){\n        this.supabase = (0,_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/database/src/queries/inventory.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/database/src/queries/purchase-orders.ts":
/*!**************************************************************!*\
  !*** ../../packages/database/src/queries/purchase-orders.ts ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PurchaseOrderQueries: function() { return /* binding */ PurchaseOrderQueries; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client */ \"(app-pages-browser)/../../packages/database/src/client.ts\");\n\nclass PurchaseOrderQueries {\n    // Generate unique PO number\n    async generatePONumber() {\n        const prefix = \"PO\";\n        const timestamp = Date.now().toString().slice(-6);\n        const random = Math.floor(Math.random() * 1000).toString().padStart(3, \"0\");\n        return \"\".concat(prefix).concat(timestamp).concat(random);\n    }\n    async getAll() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 50, offset = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        const { data, error } = await this.supabase.from(\"purchase_orders\").select(\"\\n        *,\\n        vendors (\\n          id,\\n          name,\\n          contact_email,\\n          preferred_currency\\n        ),\\n        purchase_order_items (*)\\n      \").order(\"created_at\", {\n            ascending: false\n        }).range(offset, offset + limit - 1);\n        if (error) throw error;\n        return data;\n    }\n    async getById(id) {\n        const { data, error } = await this.supabase.from(\"purchase_orders\").select(\"\\n        *,\\n        vendors (\\n          id,\\n          name,\\n          contact_email,\\n          preferred_currency,\\n          address_line1,\\n          city,\\n          country\\n        ),\\n        purchase_order_items (\\n          *,\\n          products (\\n            id,\\n            name,\\n            sku,\\n            purchase_price\\n          )\\n        )\\n      \").eq(\"id\", id).single();\n        if (error) throw error;\n        return data;\n    }\n    async getByVendor(vendorId) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n        const { data, error } = await this.supabase.from(\"purchase_orders\").select(\"\\n        *,\\n        purchase_order_items (*)\\n      \").eq(\"vendor_id\", vendorId).order(\"created_at\", {\n            ascending: false\n        }).limit(limit);\n        if (error) throw error;\n        return data;\n    }\n    async getByStatus(status) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 50;\n        const { data, error } = await this.supabase.from(\"purchase_orders\").select(\"\\n        *,\\n        vendors (\\n          id,\\n          name,\\n          contact_email\\n        ),\\n        purchase_order_items (*)\\n      \").eq(\"status\", status).order(\"created_at\", {\n            ascending: false\n        }).limit(limit);\n        if (error) throw error;\n        return data;\n    }\n    async create(purchaseOrderData) {\n        const { purchase_order, items } = purchaseOrderData;\n        // Generate PO number if not provided\n        const poNumber = purchase_order.po_number || await this.generatePONumber();\n        // Create the purchase order\n        const { data: newPO, error: poError } = await this.supabase.from(\"purchase_orders\").insert({\n            ...purchase_order,\n            po_number: poNumber\n        }).select().single();\n        if (poError) throw poError;\n        // Insert purchase order items\n        const itemsWithPOId = items.map((item)=>({\n                ...item,\n                purchase_order_id: newPO.id\n            }));\n        const { data: newItems, error: itemsError } = await this.supabase.from(\"purchase_order_items\").insert(itemsWithPOId).select();\n        if (itemsError) {\n            // Rollback PO if items insertion fails\n            await this.supabase.from(\"purchase_orders\").delete().eq(\"id\", newPO.id);\n            throw itemsError;\n        }\n        // Return the complete PO with items\n        return {\n            ...newPO,\n            purchase_order_items: newItems\n        };\n    }\n    async update(id, updates) {\n        const { data, error } = await this.supabase.from(\"purchase_orders\").update(updates).eq(\"id\", id).select().single();\n        if (error) throw error;\n        return data;\n    }\n    async updateStatus(id, status, notes) {\n        const updates = {\n            status\n        };\n        if (notes) {\n            updates.internal_notes = notes;\n        }\n        return this.update(id, updates);\n    }\n    async receiveItems(poId, receivedItems) {\n        // Update each item with received quantities\n        for (const item of receivedItems){\n            const { error } = await this.supabase.from(\"purchase_order_items\").update({\n                quantity_received: item.quantity_received,\n                batch_number: item.batch_number,\n                expiry_date: item.expiry_date,\n                received_date: new Date().toISOString()\n            }).eq(\"id\", item.item_id);\n            if (error) throw error;\n            // Update product stock levels\n            const { data: poItem, error: itemError } = await this.supabase.from(\"purchase_order_items\").select(\"product_id, quantity_received\").eq(\"id\", item.item_id).single();\n            if (itemError) continue;\n            if (poItem.product_id) {\n                // Get current stock\n                const { data: product, error: productError } = await this.supabase.from(\"products\").select(\"stock_quantity\").eq(\"id\", poItem.product_id).single();\n                if (productError) continue;\n                // Update stock\n                await this.supabase.from(\"products\").update({\n                    stock_quantity: product.stock_quantity + item.quantity_received\n                }).eq(\"id\", poItem.product_id);\n            }\n        }\n        // Check if PO is fully received\n        const { data: poItems, error: itemsError } = await this.supabase.from(\"purchase_order_items\").select(\"quantity_ordered, quantity_received\").eq(\"purchase_order_id\", poId);\n        if (itemsError) throw itemsError;\n        const isFullyReceived = poItems.every((item)=>item.quantity_received >= item.quantity_ordered);\n        const isPartiallyReceived = poItems.some((item)=>item.quantity_received > 0);\n        // Update PO status\n        let newStatus = \"sent\";\n        if (isFullyReceived) {\n            newStatus = \"received\";\n        } else if (isPartiallyReceived) {\n            newStatus = \"partially_received\";\n        }\n        await this.updateStatus(poId, newStatus);\n        return true;\n    }\n    async getStats() {\n        // Get total POs count\n        const { data: totalPOs, error: totalError } = await this.supabase.from(\"purchase_orders\").select(\"id\", {\n            count: \"exact\"\n        });\n        // Get pending POs count\n        const { data: pendingPOs, error: pendingError } = await this.supabase.from(\"purchase_orders\").select(\"id\", {\n            count: \"exact\"\n        }).in(\"status\", [\n            \"draft\",\n            \"pending_approval\",\n            \"approved\",\n            \"sent\"\n        ]);\n        // Get this month's PO value\n        const startOfMonth = new Date();\n        startOfMonth.setDate(1);\n        startOfMonth.setHours(0, 0, 0, 0);\n        const { data: monthlyPOs, error: monthlyError } = await this.supabase.from(\"purchase_orders\").select(\"total_amount_awg\").gte(\"created_at\", startOfMonth.toISOString());\n        if (totalError || pendingError || monthlyError) {\n            throw totalError || pendingError || monthlyError;\n        }\n        const monthlyTotal = (monthlyPOs === null || monthlyPOs === void 0 ? void 0 : monthlyPOs.reduce((sum, po)=>sum + (po.total_amount_awg || 0), 0)) || 0;\n        return {\n            total: (totalPOs === null || totalPOs === void 0 ? void 0 : totalPOs.length) || 0,\n            pending: (pendingPOs === null || pendingPOs === void 0 ? void 0 : pendingPOs.length) || 0,\n            monthlyTotal\n        };\n    }\n    async search(query) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 50;\n        const { data, error } = await this.supabase.from(\"purchase_orders\").select(\"\\n        *,\\n        vendors (\\n          id,\\n          name,\\n          contact_email\\n        ),\\n        purchase_order_items (*)\\n      \").or(\"po_number.ilike.%\".concat(query, \"%,notes.ilike.%\").concat(query, \"%\")).order(\"created_at\", {\n            ascending: false\n        }).limit(limit);\n        if (error) throw error;\n        return data;\n    }\n    constructor(){\n        this.supabase = (0,_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/database/src/queries/purchase-orders.ts\n"));

/***/ })

});