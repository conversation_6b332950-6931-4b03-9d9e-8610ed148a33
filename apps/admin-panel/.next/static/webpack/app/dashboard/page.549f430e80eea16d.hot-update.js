"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-triangle.js":
/*!*****************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-triangle.js ***!
  \*****************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AlertTriangle; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst AlertTriangle = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"AlertTriangle\", [\n  [\n    \"path\",\n    {\n      d: \"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z\",\n      key: \"c3ski4\"\n    }\n  ],\n  [\"path\", { d: \"M12 9v4\", key: \"juzpu7\" }],\n  [\"path\", { d: \"M12 17h.01\", key: \"p32p05\" }]\n]);\n\n\n//# sourceMappingURL=alert-triangle.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbHVjaWRlLXJlYWN0QDAuMzAzLjBfcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvYWxlcnQtdHJpYW5nbGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXNEOztBQUV0RCxzQkFBc0IsZ0VBQWdCO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSw2QkFBNkI7QUFDMUMsYUFBYSxnQ0FBZ0M7QUFDN0M7O0FBRW9DO0FBQ3BDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbHVjaWRlLXJlYWN0QDAuMzAzLjBfcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvYWxlcnQtdHJpYW5nbGUuanM/YzMwZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIGx1Y2lkZS1yZWFjdCB2MC4zMDMuMCAtIElTQ1xuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIElTQyBsaWNlbnNlLlxuICogU2VlIHRoZSBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbi5qcyc7XG5cbmNvbnN0IEFsZXJ0VHJpYW5nbGUgPSBjcmVhdGVMdWNpZGVJY29uKFwiQWxlcnRUcmlhbmdsZVwiLCBbXG4gIFtcbiAgICBcInBhdGhcIixcbiAgICB7XG4gICAgICBkOiBcIm0yMS43MyAxOC04LTE0YTIgMiAwIDAgMC0zLjQ4IDBsLTggMTRBMiAyIDAgMCAwIDQgMjFoMTZhMiAyIDAgMCAwIDEuNzMtM1pcIixcbiAgICAgIGtleTogXCJjM3NraTRcIlxuICAgIH1cbiAgXSxcbiAgW1wicGF0aFwiLCB7IGQ6IFwiTTEyIDl2NFwiLCBrZXk6IFwianV6cHU3XCIgfV0sXG4gIFtcInBhdGhcIiwgeyBkOiBcIk0xMiAxN2guMDFcIiwga2V5OiBcInAzMnAwNVwiIH1dXG5dKTtcblxuZXhwb3J0IHsgQWxlcnRUcmlhbmdsZSBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1hbGVydC10cmlhbmdsZS5qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-triangle.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/clock.js":
/*!********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/clock.js ***!
  \********************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Clock; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.303.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst Clock = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Clock\", [\n  [\"circle\", { cx: \"12\", cy: \"12\", r: \"10\", key: \"1mglay\" }],\n  [\"polyline\", { points: \"12 6 12 12 16 14\", key: \"68esgv\" }]\n]);\n\n\n//# sourceMappingURL=clock.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbHVjaWRlLXJlYWN0QDAuMzAzLjBfcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2xvY2suanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXNEOztBQUV0RCxjQUFjLGdFQUFnQjtBQUM5QixlQUFlLDRDQUE0QztBQUMzRCxpQkFBaUIsMkNBQTJDO0FBQzVEOztBQUU0QjtBQUM1QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2x1Y2lkZS1yZWFjdEAwLjMwMy4wX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL2Nsb2NrLmpzPzNhNGYiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuMzAzLjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBDbG9jayA9IGNyZWF0ZUx1Y2lkZUljb24oXCJDbG9ja1wiLCBbXG4gIFtcImNpcmNsZVwiLCB7IGN4OiBcIjEyXCIsIGN5OiBcIjEyXCIsIHI6IFwiMTBcIiwga2V5OiBcIjFtZ2xheVwiIH1dLFxuICBbXCJwb2x5bGluZVwiLCB7IHBvaW50czogXCIxMiA2IDEyIDEyIDE2IDE0XCIsIGtleTogXCI2OGVzZ3ZcIiB9XVxuXSk7XG5cbmV4cG9ydCB7IENsb2NrIGFzIGRlZmF1bHQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNsb2NrLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/clock.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth-context */ \"(app-pages-browser)/./src/lib/auth-context.tsx\");\n/* harmony import */ var _nutripro_database__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @nutripro/database */ \"(app-pages-browser)/../../packages/database/src/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button!=!@nutripro/ui */ \"(app-pages-browser)/__barrel_optimize__?names=Button!=!../../packages/ui/src/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardContent,CardDescription,CardHeader,CardTitle!=!@nutripro/ui */ \"(app-pages-browser)/__barrel_optimize__?names=Card,CardContent,CardDescription,CardHeader,CardTitle!=!../../packages/ui/src/index.ts\");\n/* harmony import */ var _components_dashboard_SalesMetricsSection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/SalesMetricsSection */ \"(app-pages-browser)/./src/components/dashboard/SalesMetricsSection.tsx\");\n/* harmony import */ var _components_dashboard_RevenueChartsSection__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/RevenueChartsSection */ \"(app-pages-browser)/./src/components/dashboard/RevenueChartsSection.tsx\");\n/* harmony import */ var _components_dashboard_InventoryAlertsSection__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/dashboard/InventoryAlertsSection */ \"(app-pages-browser)/./src/components/dashboard/InventoryAlertsSection.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const { user } = (0,_lib_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalProducts: 0,\n        lowStockProducts: 0,\n        totalCustomers: 0,\n        retailCustomers: 0,\n        wholesaleCustomers: 0,\n        loading: true\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadStats = async ()=>{\n            try {\n                const productQueries = new _nutripro_database__WEBPACK_IMPORTED_MODULE_3__.ProductQueries();\n                const customerQueries = new _nutripro_database__WEBPACK_IMPORTED_MODULE_3__.CustomerQueries();\n                const [products, lowStock, customerStats] = await Promise.all([\n                    productQueries.getAll(),\n                    productQueries.getLowStock(10),\n                    customerQueries.getStats()\n                ]);\n                setStats({\n                    totalProducts: (products === null || products === void 0 ? void 0 : products.length) || 0,\n                    lowStockProducts: (lowStock === null || lowStock === void 0 ? void 0 : lowStock.length) || 0,\n                    totalCustomers: customerStats.total,\n                    retailCustomers: customerStats.retail,\n                    wholesaleCustomers: customerStats.wholesale,\n                    loading: false\n                });\n            } catch (error) {\n                console.error(\"Failed to load dashboard stats:\", error);\n                setStats((prev)=>({\n                        ...prev,\n                        loading: false\n                    }));\n            }\n        };\n        loadStats();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4 md:p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 md:mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl md:text-3xl font-bold text-gray-900\",\n                            children: \"Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-1\",\n                            children: [\n                                \"Welcome back, \",\n                                (user === null || user === void 0 ? void 0 : user.email) || \"Demo User\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6 md:mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: \"Total Products\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: stats.loading ? \"...\" : stats.totalProducts\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-blue-600\",\n                                            children: \"Active inventory items\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: \"Total Customers\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: stats.loading ? \"...\" : stats.totalCustomers\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-green-600\",\n                                            children: [\n                                                stats.retailCustomers,\n                                                \" retail, \",\n                                                stats.wholesaleCustomers,\n                                                \" wholesale\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: \"Retail Customers\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: stats.loading ? \"...\" : stats.retailCustomers\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-blue-600\",\n                                            children: \"Individual customers\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: \"Low Stock Items\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold \".concat(stats.lowStockProducts > 0 ? \"text-red-600\" : \"text-green-600\"),\n                                            children: stats.loading ? \"...\" : stats.lowStockProducts\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs \".concat(stats.lowStockProducts > 0 ? \"text-red-600\" : \"text-green-600\"),\n                                            children: stats.lowStockProducts > 0 ? \"Requires attention\" : \"All items well stocked\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_SalesMetricsSection__WEBPACK_IMPORTED_MODULE_6__.SalesMetricsSection, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_RevenueChartsSection__WEBPACK_IMPORTED_MODULE_7__.RevenueChartsSection, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_InventoryAlertsSection__WEBPACK_IMPORTED_MODULE_8__.InventoryAlertsSection, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            children: \"Recent Orders\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: \"Latest customer orders\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Order #1234\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 141,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"John Doe\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 142,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: \"$89.99\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 145,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-green-600\",\n                                                                children: \"Completed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 146,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Order #1235\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Jane Smith\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 152,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: \"$156.50\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-yellow-600\",\n                                                                children: \"Processing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            children: \"Quick Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            children: \"Common tasks and shortcuts\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                className: \"h-20 flex-col\",\n                                                onClick: ()=>window.open(\"/test-db\", \"_blank\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg mb-1\",\n                                                        children: \"\\uD83D\\uDDC4️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Test Database\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                className: \"h-20 flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg mb-1\",\n                                                        children: \"\\uD83D\\uDCE6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Manage Products\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                className: \"h-20 flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg mb-1\",\n                                                        children: \"\\uD83D\\uDC65\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"View Customers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                className: \"h-20 flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg mb-1\",\n                                                        children: \"\\uD83C\\uDFC3‍♂️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Coach Program\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/page.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"SgovsmiKK+PKqljzAFZvdFQHLr0=\", false, function() {\n    return [\n        _lib_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/InventoryAlertsSection.tsx":
/*!*************************************************************!*\
  !*** ./src/components/dashboard/InventoryAlertsSection.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InventoryAlertsSection: function() { return /* binding */ InventoryAlertsSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardContent,CardDescription,CardHeader,CardTitle!=!@nutripro/ui */ \"(app-pages-browser)/__barrel_optimize__?names=Card,CardContent,CardDescription,CardHeader,CardTitle!=!../../packages/ui/src/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Badge_nutripro_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Badge!=!@nutripro/ui */ \"(app-pages-browser)/__barrel_optimize__?names=Badge!=!../../packages/ui/src/index.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Package_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Package,TrendingDown!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Package_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Package,TrendingDown!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Package_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Package,TrendingDown!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Package_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Package,TrendingDown!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _nutripro_database__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @nutripro/database */ \"(app-pages-browser)/../../packages/database/src/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/cartesian/Bar.js\");\n/* __next_internal_client_entry_do_not_use__ InventoryAlertsSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Mock data generators\nconst generateInventoryAlerts = (products)=>{\n    const alerts = [];\n    products === null || products === void 0 ? void 0 : products.forEach((product)=>{\n        if (product.stock_quantity <= product.min_stock_level) {\n            alerts.push({\n                id: product.id,\n                productName: product.name,\n                sku: product.sku,\n                currentStock: product.stock_quantity,\n                minStock: product.min_stock_level,\n                alertType: product.stock_quantity === 0 ? \"out_of_stock\" : \"low_stock\",\n                severity: product.stock_quantity === 0 ? \"high\" : product.stock_quantity <= product.min_stock_level / 2 ? \"medium\" : \"low\"\n            });\n        }\n        // Add some mock expiry warnings\n        if (Math.random() > 0.8) {\n            const daysUntilExpiry = Math.floor(Math.random() * 30) + 1;\n            alerts.push({\n                id: \"\".concat(product.id, \"-expiry\"),\n                productName: product.name,\n                sku: product.sku,\n                currentStock: product.stock_quantity,\n                minStock: product.min_stock_level,\n                alertType: \"expiry_warning\",\n                severity: daysUntilExpiry <= 7 ? \"high\" : daysUntilExpiry <= 14 ? \"medium\" : \"low\",\n                daysUntilExpiry\n            });\n        }\n    });\n    return alerts;\n};\nconst generateStockLevelData = ()=>{\n    return [\n        {\n            category: \"Protein Powders\",\n            inStock: 45,\n            lowStock: 8,\n            outOfStock: 2\n        },\n        {\n            category: \"Pre-Workout\",\n            inStock: 32,\n            lowStock: 5,\n            outOfStock: 1\n        },\n        {\n            category: \"Vitamins\",\n            inStock: 28,\n            lowStock: 3,\n            outOfStock: 0\n        },\n        {\n            category: \"BCAA/Amino\",\n            inStock: 22,\n            lowStock: 4,\n            outOfStock: 1\n        },\n        {\n            category: \"Creatine\",\n            inStock: 18,\n            lowStock: 2,\n            outOfStock: 0\n        },\n        {\n            category: \"Fat Burners\",\n            inStock: 15,\n            lowStock: 3,\n            outOfStock: 1\n        }\n    ];\n};\nfunction InventoryAlertsSection() {\n    _s();\n    const [alerts, setAlerts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stockLevelData, setStockLevelData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadInventoryData = async ()=>{\n            try {\n                const productQueries = new _nutripro_database__WEBPACK_IMPORTED_MODULE_4__.ProductQueries();\n                const products = await productQueries.getAll();\n                setAlerts(generateInventoryAlerts(products));\n                setStockLevelData(generateStockLevelData());\n            } catch (error) {\n                console.error(\"Failed to load inventory data:\", error);\n                // Use mock data on error\n                setStockLevelData(generateStockLevelData());\n            } finally{\n                setLoading(false);\n            }\n        };\n        loadInventoryData();\n    }, []);\n    const getSeverityColor = (severity)=>{\n        switch(severity){\n            case \"high\":\n                return \"bg-red-100 text-red-800 border-red-200\";\n            case \"medium\":\n                return \"bg-yellow-100 text-yellow-800 border-yellow-200\";\n            case \"low\":\n                return \"bg-blue-100 text-blue-800 border-blue-200\";\n            default:\n                return \"bg-gray-100 text-gray-800 border-gray-200\";\n        }\n    };\n    const getAlertIcon = (alertType)=>{\n        switch(alertType){\n            case \"out_of_stock\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Package_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 35\n                }, this);\n            case \"low_stock\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Package_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 32\n                }, this);\n            case \"expiry_warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Package_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 37\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Package_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getAlertMessage = (alert)=>{\n        switch(alert.alertType){\n            case \"out_of_stock\":\n                return \"Out of stock\";\n            case \"low_stock\":\n                return \"Low stock: \".concat(alert.currentStock, \" remaining (min: \").concat(alert.minStock, \")\");\n            case \"expiry_warning\":\n                return \"Expires in \".concat(alert.daysUntilExpiry, \" days\");\n            default:\n                return \"Unknown alert\";\n        }\n    };\n    const stockStatusColors = [\n        \"#10B981\",\n        \"#F59E0B\",\n        \"#EF4444\"\n    ] // Green, Yellow, Red\n    ;\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-6 md:mb-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900\",\n                            children: \"Inventory Management\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Stock levels, alerts, and inventory insights\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6\",\n                    children: [\n                        1,\n                        2\n                    ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-gray-200 rounded animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-64 bg-gray-100 rounded animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n            lineNumber: 146,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-6 md:mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-900\",\n                        children: \"Inventory Management\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Stock levels, alerts, and inventory insights\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Package_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5 text-orange-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Inventory Alerts\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: [\n                                            alerts.length,\n                                            \" active alerts requiring attention\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 max-h-64 overflow-y-auto\",\n                                    children: alerts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Package_TrendingDown_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-8 w-8 mx-auto mb-2 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"No inventory alerts\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: \"All products are well stocked\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 17\n                                    }, this) : alerts.map((alert)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3 p-3 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-1 rounded \".concat(getSeverityColor(alert.severity)),\n                                                    children: getAlertIcon(alert.alertType)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-sm truncate\",\n                                                            children: alert.productName\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"SKU: \",\n                                                                alert.sku\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-600 mt-1\",\n                                                            children: getAlertMessage(alert)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Badge_nutripro_ui__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: getSeverityColor(alert.severity),\n                                                    children: alert.severity\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, alert.id, true, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        children: \"Stock Levels by Category\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"Inventory status across product categories\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 250,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.BarChart, {\n                                            data: stockLevelData,\n                                            layout: \"horizontal\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.CartesianGrid, {\n                                                    strokeDasharray: \"3 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.XAxis, {\n                                                    type: \"number\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.YAxis, {\n                                                    dataKey: \"category\",\n                                                    type: \"category\",\n                                                    width: 100\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.Tooltip, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__.Bar, {\n                                                    dataKey: \"inStock\",\n                                                    stackId: \"a\",\n                                                    fill: \"#10B981\",\n                                                    name: \"In Stock\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__.Bar, {\n                                                    dataKey: \"lowStock\",\n                                                    stackId: \"a\",\n                                                    fill: \"#F59E0B\",\n                                                    name: \"Low Stock\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__.Bar, {\n                                                    dataKey: \"outOfStock\",\n                                                    stackId: \"a\",\n                                                    fill: \"#EF4444\",\n                                                    name: \"Out of Stock\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 flex justify-center space-x-6 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-green-500 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"In Stock\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-yellow-500 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Low Stock\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-red-500 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Out of Stock\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/dashboard/InventoryAlertsSection.tsx\",\n        lineNumber: 168,\n        columnNumber: 5\n    }, this);\n}\n_s(InventoryAlertsSection, \"T6fZd0ysFKPM9WSxsv9mcK8NZ9o=\");\n_c = InventoryAlertsSection;\nvar _c;\n$RefreshReg$(_c, \"InventoryAlertsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/InventoryAlertsSection.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/__barrel_optimize__?names=Badge!=!../../packages/ui/src/index.ts":
/*!************************************************************************!*\
  !*** __barrel_optimize__?names=Badge!=!../../packages/ui/src/index.ts ***!
  \************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: function() { return /* reexport safe */ _Users_ivinroekiman_Desktop_NutriPro_packages_ui_src_components_ui_badge_tsx__WEBPACK_IMPORTED_MODULE_0__.Badge; }\n/* harmony export */ });\n/* harmony import */ var _Users_ivinroekiman_Desktop_NutriPro_packages_ui_src_components_ui_badge_tsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../packages/ui/src/components/ui/badge.tsx */ \"(app-pages-browser)/../../packages/ui/src/components/ui/badge.tsx\");\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUJhZGdlIT0hLi4vLi4vcGFja2FnZXMvdWkvc3JjL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQ29HIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy91aS9zcmMvaW5kZXgudHM/YzJkMSJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IEJhZGdlIH0gZnJvbSBcIi9Vc2Vycy9pdmlucm9la2ltYW4vRGVza3RvcC9OdXRyaVByby9wYWNrYWdlcy91aS9zcmMvY29tcG9uZW50cy91aS9iYWRnZS50c3hcIiJdLCJuYW1lcyI6WyJCYWRnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/__barrel_optimize__?names=Badge!=!../../packages/ui/src/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/ui/src/components/ui/badge.tsx":
/*!*****************************************************!*\
  !*** ../../packages/ui/src/components/ui/badge.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: function() { return /* binding */ Badge; }\n/* harmony export */ });\n// Placeholder for badge component\nconst Badge = ()=>null;\n_c = Badge;\nvar _c;\n$RefreshReg$(_c, \"Badge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy91aS9zcmMvY29tcG9uZW50cy91aS9iYWRnZS50c3giLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGtDQUFrQztBQUMzQixNQUFNQSxRQUFRLElBQU0sS0FBSztLQUFuQkEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL3VpL3NyYy9jb21wb25lbnRzL3VpL2JhZGdlLnRzeD8zMDk4Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIFBsYWNlaG9sZGVyIGZvciBiYWRnZSBjb21wb25lbnRcbmV4cG9ydCBjb25zdCBCYWRnZSA9ICgpID0+IG51bGw7XG4iXSwibmFtZXMiOlsiQmFkZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/ui/src/components/ui/badge.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/chart/BarChart.js":
/*!**************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/chart/BarChart.js ***!
  \**************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BarChart: function() { return /* binding */ BarChart; }\n/* harmony export */ });\n/* harmony import */ var _generateCategoricalChart__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./generateCategoricalChart */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/chart/generateCategoricalChart.js\");\n/* harmony import */ var _cartesian_Bar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../cartesian/Bar */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _cartesian_XAxis__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../cartesian/XAxis */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _cartesian_YAxis__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../cartesian/YAxis */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _util_CartesianUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../util/CartesianUtils */ \"(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/util/CartesianUtils.js\");\n/**\n * @fileOverview Bar Chart\n */\n\n\n\n\n\nvar BarChart = (0,_generateCategoricalChart__WEBPACK_IMPORTED_MODULE_0__.generateCategoricalChart)({\n  chartName: 'BarChart',\n  GraphicalChild: _cartesian_Bar__WEBPACK_IMPORTED_MODULE_1__.Bar,\n  defaultTooltipEventType: 'axis',\n  validateTooltipEventTypes: ['axis', 'item'],\n  axisComponents: [{\n    axisType: 'xAxis',\n    AxisComp: _cartesian_XAxis__WEBPACK_IMPORTED_MODULE_2__.XAxis\n  }, {\n    axisType: 'yAxis',\n    AxisComp: _cartesian_YAxis__WEBPACK_IMPORTED_MODULE_3__.YAxis\n  }],\n  formatAxisMap: _util_CartesianUtils__WEBPACK_IMPORTED_MODULE_4__.formatAxisMap\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVjaGFydHNAMi4xNS40X3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9yZWNoYXJ0cy9lczYvY2hhcnQvQmFyQ2hhcnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ3NFO0FBQy9CO0FBQ0k7QUFDQTtBQUNZO0FBQ2hELGVBQWUsbUZBQXdCO0FBQzlDO0FBQ0Esa0JBQWtCLCtDQUFHO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyxtREFBSztBQUNuQixHQUFHO0FBQ0g7QUFDQSxjQUFjLG1EQUFLO0FBQ25CLEdBQUc7QUFDSCxpQkFBaUIsK0RBQWE7QUFDOUIsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL3JlY2hhcnRzQDIuMTUuNF9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvcmVjaGFydHMvZXM2L2NoYXJ0L0JhckNoYXJ0LmpzPzhkOTMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAZmlsZU92ZXJ2aWV3IEJhciBDaGFydFxuICovXG5pbXBvcnQgeyBnZW5lcmF0ZUNhdGVnb3JpY2FsQ2hhcnQgfSBmcm9tICcuL2dlbmVyYXRlQ2F0ZWdvcmljYWxDaGFydCc7XG5pbXBvcnQgeyBCYXIgfSBmcm9tICcuLi9jYXJ0ZXNpYW4vQmFyJztcbmltcG9ydCB7IFhBeGlzIH0gZnJvbSAnLi4vY2FydGVzaWFuL1hBeGlzJztcbmltcG9ydCB7IFlBeGlzIH0gZnJvbSAnLi4vY2FydGVzaWFuL1lBeGlzJztcbmltcG9ydCB7IGZvcm1hdEF4aXNNYXAgfSBmcm9tICcuLi91dGlsL0NhcnRlc2lhblV0aWxzJztcbmV4cG9ydCB2YXIgQmFyQ2hhcnQgPSBnZW5lcmF0ZUNhdGVnb3JpY2FsQ2hhcnQoe1xuICBjaGFydE5hbWU6ICdCYXJDaGFydCcsXG4gIEdyYXBoaWNhbENoaWxkOiBCYXIsXG4gIGRlZmF1bHRUb29sdGlwRXZlbnRUeXBlOiAnYXhpcycsXG4gIHZhbGlkYXRlVG9vbHRpcEV2ZW50VHlwZXM6IFsnYXhpcycsICdpdGVtJ10sXG4gIGF4aXNDb21wb25lbnRzOiBbe1xuICAgIGF4aXNUeXBlOiAneEF4aXMnLFxuICAgIEF4aXNDb21wOiBYQXhpc1xuICB9LCB7XG4gICAgYXhpc1R5cGU6ICd5QXhpcycsXG4gICAgQXhpc0NvbXA6IFlBeGlzXG4gIH1dLFxuICBmb3JtYXRBeGlzTWFwOiBmb3JtYXRBeGlzTWFwXG59KTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/recharts@2.15.4_react-dom@18.3.1_react@18.3.1/node_modules/recharts/es6/chart/BarChart.js\n"));

/***/ })

});