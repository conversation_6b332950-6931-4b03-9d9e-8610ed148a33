"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/orders/page",{

/***/ "(app-pages-browser)/../../packages/database/src/types/supabase.ts":
/*!*****************************************************!*\
  !*** ../../packages/database/src/types/supabase.ts ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n// NutriPro Database Types - Generated from Schema\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/database/src/types/supabase.ts\n"));

/***/ })

});