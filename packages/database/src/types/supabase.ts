// NutriPro Database Types - Generated from Schema
export type UserRole = 'admin' | 'manager' | 'staff' | 'viewer'
export type CustomerType = 'retail' | 'wholesale'
export type TransactionStatus = 'pending' | 'processing' | 'completed' | 'cancelled' | 'refunded'
export type PaymentMethod = 'cash' | 'card' | 'bank_transfer' | 'store_credit'
export type TransactionType = 'sale' | 'return' | 'wholesale_order' | 'adjustment'
export type BatchStatus = 'active' | 'expired' | 'recalled' | 'depleted'
export type BatchMovementType = 'received' | 'sold' | 'expired' | 'returned' | 'adjusted'
export type PurchaseOrderStatus = 'draft' | 'pending_approval' | 'approved' | 'sent' | 'partially_received' | 'received' | 'cancelled'
export type AdjustmentType = 'restock' | 'damage' | 'theft' | 'expired' | 'count_correction' | 'return'
export type MovementType = 'sale' | 'purchase' | 'adjustment' | 'transfer' | 'return'

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          role: UserRole
          first_name: string | null
          last_name: string | null
          phone: string | null
          is_active: boolean
          last_login_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          role?: UserRole
          first_name?: string | null
          last_name?: string | null
          phone?: string | null
          is_active?: boolean
          last_login_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          role?: UserRole
          first_name?: string | null
          last_name?: string | null
          phone?: string | null
          is_active?: boolean
          last_login_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      categories: {
        Row: {
          id: string
          name: string
          description: string | null
          parent_id: string | null
          sort_order: number
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          parent_id?: string | null
          sort_order?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          parent_id?: string | null
          sort_order?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      transactions: {
        Row: {
          id: string
          transaction_number: string
          customer_id: string | null
          staff_id: string | null
          transaction_type: TransactionType
          status: TransactionStatus
          subtotal: number
          tax_rate: number
          tax_amount: number
          discount_amount: number
          total_amount: number
          payment_method: PaymentMethod | null
          payment_reference: string | null
          notes: string | null
          internal_notes: string | null
          synced_at: string | null
          sync_version: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          transaction_number: string
          customer_id?: string | null
          staff_id?: string | null
          transaction_type: TransactionType
          status?: TransactionStatus
          subtotal: number
          tax_rate?: number
          tax_amount?: number
          discount_amount?: number
          total_amount: number
          payment_method?: PaymentMethod | null
          payment_reference?: string | null
          notes?: string | null
          internal_notes?: string | null
          synced_at?: string | null
          sync_version?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          transaction_number?: string
          customer_id?: string | null
          staff_id?: string | null
          transaction_type?: TransactionType
          status?: TransactionStatus
          subtotal?: number
          tax_rate?: number
          tax_amount?: number
          discount_amount?: number
          total_amount?: number
          payment_method?: PaymentMethod | null
          payment_reference?: string | null
          notes?: string | null
          internal_notes?: string | null
          synced_at?: string | null
          sync_version?: number
          created_at?: string
          updated_at?: string
        }
      }
      transaction_items: {
        Row: {
          id: string
          transaction_id: string
          product_id: string | null
          quantity: number
          unit_price: number
          discount_amount: number
          line_total: number
          product_name: string
          product_sku: string
          batch_number: string | null
          expiry_date: string | null
          created_at: string
        }
        Insert: {
          id?: string
          transaction_id: string
          product_id?: string | null
          quantity: number
          unit_price: number
          discount_amount?: number
          line_total: number
          product_name: string
          product_sku: string
          batch_number?: string | null
          expiry_date?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          transaction_id?: string
          product_id?: string | null
          quantity?: number
          unit_price?: number
          discount_amount?: number
          line_total?: number
          product_name?: string
          product_sku?: string
          batch_number?: string | null
          expiry_date?: string | null
          created_at?: string
        }
      }
      product_batches: {
        Row: {
          id: string
          product_id: string | null
          variant_id: string | null
          batch_number: string
          expiry_date: string
          received_date: string
          quantity_received: number
          quantity_available: number
          quantity_sold: number
          quantity_expired: number
          quantity_returned: number
          unit_cost: number
          total_cost: number
          fifo_priority: number
          status: BatchStatus
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          product_id?: string | null
          variant_id?: string | null
          batch_number: string
          expiry_date: string
          received_date: string
          quantity_received: number
          quantity_available: number
          quantity_sold?: number
          quantity_expired?: number
          quantity_returned?: number
          unit_cost: number
          total_cost: number
          fifo_priority: number
          status?: BatchStatus
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          product_id?: string | null
          variant_id?: string | null
          batch_number?: string
          expiry_date?: string
          received_date?: string
          quantity_received?: number
          quantity_available?: number
          quantity_sold?: number
          quantity_expired?: number
          quantity_returned?: number
          unit_cost?: number
          total_cost?: number
          fifo_priority?: number
          status?: BatchStatus
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      purchase_orders: {
        Row: {
          id: string
          po_number: string
          vendor_id: string | null
          status: PurchaseOrderStatus
          order_date: string
          expected_delivery_date: string | null
          actual_delivery_date: string | null
          currency: string
          subtotal: number
          tax_amount: number
          shipping_cost: number
          discount_amount: number
          total_amount: number
          total_amount_awg: number | null
          exchange_rate: number
          notes: string | null
          internal_notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          po_number: string
          vendor_id?: string | null
          status?: PurchaseOrderStatus
          order_date?: string
          expected_delivery_date?: string | null
          actual_delivery_date?: string | null
          currency?: string
          subtotal?: number
          tax_amount?: number
          shipping_cost?: number
          discount_amount?: number
          total_amount?: number
          total_amount_awg?: number | null
          exchange_rate?: number
          notes?: string | null
          internal_notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          po_number?: string
          vendor_id?: string | null
          status?: PurchaseOrderStatus
          order_date?: string
          expected_delivery_date?: string | null
          actual_delivery_date?: string | null
          currency?: string
          subtotal?: number
          tax_amount?: number
          shipping_cost?: number
          discount_amount?: number
          total_amount?: number
          total_amount_awg?: number | null
          exchange_rate?: number
          notes?: string | null
          internal_notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      user_role: UserRole
      customer_type: CustomerType
      transaction_status: TransactionStatus
      payment_method: PaymentMethod
      transaction_type: TransactionType
      batch_status_enum: BatchStatus
      batch_movement_enum: BatchMovementType
      po_status_enum: PurchaseOrderStatus
      adjustment_type_enum: AdjustmentType
      movement_type_enum: MovementType
    }
  }
}